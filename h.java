import java.util.Random;
import javax.microedition.lcdui.Canvas;
import javax.microedition.lcdui.Display;
import javax.microedition.lcdui.Graphics;
import javax.microedition.lcdui.Image;
import javax.microedition.rms.InvalidRecordIDException;
import javax.microedition.rms.RecordComparator;
import javax.microedition.rms.RecordEnumeration;
import javax.microedition.rms.RecordFilter;
import javax.microedition.rms.RecordStore;
import javax.microedition.rms.RecordStoreException;

public final class h extends Canvas implements Runnable {
   static byte[][] a;
   static byte[][][] b;
   static byte[] c;
   static int[] d = new int[]{0, 25, -1, 1, 25, 35, -1, 1, 35, 40, -1, 2, 40, 100, -1, 2, 50, 100, 100, 2};
   static byte[] e = new byte[252];
   static byte[] f = new byte[]{22, 16, 0, 0, 7, 2, 1, 3, 1, 0, 2, 5, -1, -1, -1, -1, -1, -1, -1, 0, 0, 4, 0, 6, 3, -1, -1, -1, -1, 18, 24, 6, 0, 8, 16, 4, 4, 1, 2, 5, 2, 1, 7, 1, 7, 7, 7, 1, -1, 0, 1, 5, 6, 5, 7, 6, 4, 4, 4, -1, -1, -1, -1, -1};
   static short[] g;
   static int h;
   static int i;
   static boolean j = false;
   static boolean k;
   static boolean l;
   static byte m = -1;
   static d[] n;
   static a[] o;
   static a[][] p;
   static byte[][] q;
   static byte[][] r;
   static g[] s;
   static d[] t;
   static a[] u;
   static d[] v;
   static byte w;
   static byte x;
   static byte y;
   static byte z;
   static byte A = -1;
   static byte B;
   static int C;
   static int D;
   static byte[] E = new byte[]{-1, -1, -1};
   static int F = -1;
   static int G = -1;
   static int H = 0;
   static int I = -1;
   static boolean J = true;
   static int K;
   static int L;
   static a M;
   static int[] N = new int[]{-1, -1, 1, 0, 0, 0, 0};
   static boolean O = false;
   static boolean P = false;
   static Image Q;
   static int[] R = new int[16];
   static int[] S = new int[16];
   static String[] T = new String[16];
   static int[] U = new int[6];
   static int[] V = null;
   static int[] W = new int[]{16754176, 16777215, 0, 16725317, 16741375, 37119, 61440, 16776960};
   static int X;
   static boolean Y;
   static boolean Z;
   static boolean aa;
   static int[] ab = new int[32];
   static int ac;
   static int[] ad = new int[64];
   static int[] ae = new int[2];
   static String af;
   static byte ag;
   static byte ah;
   static byte ai = 2;
   static boolean aj;
   static int ak;
   static int al;
   static int am;
   static int[] an;
   static boolean ao;
   static int ap = 0;
   static String[] aq = new String[]{"NONE", "TEXTS", "PLAYER ATTS", "FRIENDSHIP"};
   static byte[] ar = new byte[]{12, 11, 1, 18, 11, 1, 11, 10, 1, 11, 11, 1, 11, 9, 1, 11, 6, 5, 11, 7, 5, 11, 8, 4, 28, 9, 1, 28, 6, 5, 28, 7, 5, 28, 8, 4, 30, 9, 1, 30, 10, 1, 30, 6, 5, 30, 7, 5, 30, 8, 4, 31, 9, 1, 31, 6, 5, 31, 7, 5, 31, 8, 4, 6, 11, 1, 25, 1, 1, 25, 2, 1, 23, 3, 4, 23, 5, 4, 14, 4, 1, 14, 9, 1, 19, 4, 1};
   static boolean as;
   static boolean at;
   static boolean au;
   private static int bP;
   private static int bQ = 4;
   static final byte[] av = new byte[1];
   static byte[] aw = null;
   static byte ax = 0;
   static int ay = 0;
   static String[] az = new String[]{"ENGLISH", "FRANCAIS", "DEUTSCH", "ESPANOL", "ITALIANO", "PORTUGUES-BR", "POLSKA"};
   static int[] aA = new int[]{0, 1, 2, 4, 3, 5};
   static Graphics aB = null;
   private long bR;
   static byte[] aC;
   static byte[] aD;
   static boolean aE = false;
   static long aF;
   private boolean bS = false;
   static long aG = 0L;
   static long aH = 0L;
   private static long bT = 0L;
   private static int bU = 216;
   private static int bV = 57;
   private static int bW;
   private static int bX;
   private static boolean bY;
   private static boolean bZ;
   public static boolean aI;
   public static String aJ;
   static int[] aK;
   static int[] aL;
   static Image aM;
   static Graphics aN;
   static int aO;
   static boolean aP;
   static boolean aQ;
   static boolean aR;
   static byte[] aS;
   static int[] aT;
   static int[] aU;
   static int aV;
   public static boolean aW;
   public static boolean aX;
   public static boolean aY;
   static int[] aZ;
   static int ba;
   static int[] bb;
   static boolean bc;
   static int[] bd;
   static boolean be;
   static int[] bf;
   static boolean bg;
   static int bh;
   static int[] bi;
   static int[] bj;
   static int[] bk;
   static int bl;
   static byte[] bm;
   static byte[] bn;
   static int[] bo;
   static int bp;
   static int bq;
   static int[] br;
   static int bs;
   static int bt;
   static final int[] bu;
   static final int[] bv;
   static int bw;
   static int bx;
   static boolean by;
   static int bz;
   static int bA;
   static int bB;
   static int bC;
   static int bD;
   static int bE;
   static int[] bF;
   public static boolean bG;
   public static Image bH;
   public static boolean bI;
   public static long bJ;
   public static long bK;
   public static long bL;
   static byte[] bM;
   static int[] bN;
   static int[] bO;

   static void a(int var0) {
      h = var0;
   }

   static void a(int var0, a[] var1, int var2, boolean var3, boolean var4, int var5, boolean var6) {
      byte[] var7 = GloftMMN.j(var0);
      var1[var2] = new a();
      var1[var2].a(var7, 0, var5, var6);
      if (var3) {
         var1[var2].a(0, 0, -1, -1, 0);
      }

      if (var3 && var4) {
         var1[var2].b();
      }

      if (var5 > 0) {
         var1[var2].l = var5;
      }

   }

   private static boolean a(int var0, int var1) {
      aB.setClip(0, 0, 240, 400);
      aB.setColor(0);
      if (var0 > 500) {
         var0 = 500;
      }

      int var2 = (var0 << 8) / 500;
      aB.fillRect(0, 0, 240 * var2 >> 8, 400);
      if (var1 == 0) {
         aB.fillRect(0, 0, 240 * var2 >> 8, 400);
      } else if (var1 == 1) {
         int var3 = 240 - (240 * var2 >> 8);
         aB.fillRect(240 - var3, 0, var3, 400);
      }

      return var0 >= 500;
   }

   private static void a(int var0, int var1, boolean var2) {
      if (var2) {
         ++var0;
      }

      bP = 0;
      U[1] = var0;
      U[0] = var1;

      for(int var3 = 0; var3 < var0; ++var3) {
         R[var3] = 226;
         S[var3] = -1;
         T[var3] = null;
      }

      if (var2) {
         R[var0 - 1] = 0;
         S[var0 - 1] = 21;
      }

      U[4] = -1;
      U[5] = -1;
   }

   private static void a(Graphics var0, int var1, int var2) {
      if (u[0] == null) {
         a(0, u, 0, false, false, 1, true);
      }

      int var3 = u[0].a(u[0].d(30, 7));
      int var4 = u[0].b(u[0].d(30, 7)) + 1;
      if (var1 == 796) {
         if (h != 16) {
            var0.setClip(0, 0, 240, 400);
         }

         if (!aW) {
            u[0].a((Graphics)var0, 30, 1, 0, 400 - var4, 0, 0, 0);
         } else {
            u[0].a((Graphics)var0, 30, 2, 0, 400 - var4, 0, 0, 0);
         }
      }

      if ((var1 == 901 || var1 == 849 || var1 == 848 || var1 == 401 || var1 == 875) && g.ak == null) {
         if (!aW) {
            u[0].a((Graphics)var0, 30, 10, 0, 400 - var4, 0, 0, 0);
         } else {
            u[0].a((Graphics)var0, 30, 11, 0, 400 - var4, 0, 0, 0);
         }
      }

      if (var2 == 909) {
         if (!aX) {
            u[0].a((Graphics)var0, 30, 7, 240 - var3, 400 - var4, 0, 0, 0);
         } else {
            u[0].a((Graphics)var0, 30, 8, 240 - var3, 400 - var4, 0, 0, 0);
         }
      }

      if (var2 == 794 || var2 == 0) {
         if (y == 3) {
            var0.setClip(0, 0, 240, 400);
         }

         if (!aX) {
            u[0].a((Graphics)var0, 30, 4, 240 - var3 + 5, 400 - var4, 0, 0, 0);
            return;
         }

         u[0].a((Graphics)var0, 30, 5, 240 - var3 + 5, 400 - var4, 0, 0, 0);
      }

   }

   private static void a(Graphics var0, boolean var1, boolean var2) {
      var0.setClip(0, 0, 240, 400);
      int var4 = 0;

      int var3;
      for(var3 = 0; var3 < U[1]; ++var3) {
         if (R[var3] >= 0) {
            String var5 = GloftMMN.k(R[var3]);
            if (T[var3] != null) {
               var5 = var5 + T[var3];
            }

            M.a(var5);
            if (a.y > var4) {
               var4 = a.y;
            }
         }
      }

      if (!var1) {
         M.a(GloftMMN.k(U[3]));
         if (a.y > var4) {
            var4 = a.y;
         }
      }

      M.a(GloftMMN.k(U[2]));
      if (a.y > var4) {
         var4 = a.y;
      }

      boolean var14 = false;
      int var6;
      if (bQ > U[1]) {
         var6 = U[1] * 45;
      } else {
         var6 = bQ * 45;
      }

      if (bQ < U[1]) {
         var6 += 10;
      }

      int var7 = var6 + 18 + 18 + 1;
      var4 += 52;
      int var8 = (240 - var4) / 2;
      int var9 = (400 - var7) / 2;
      if (var2) {
         int[] var11;
         var9 = (var11 = v[7].c(10))[1];
         var8 = var11[0] - var4 + 2;
      }

      var4 -= 2;
      if (!var1) {
         if (bQ < U[1]) {
            var9 += 5;
         }

         var0.setColor(16768665);
         if (K % 8 < 6) {
            var0.setColor(16775323);
         }

         if (a(20, var9 + 18 + 18 + (U[0] - bP) * 45 + -12 - 6, 200, 40)) {
            u[0].a((Graphics)var0, 28, 2, 120, var9 + 18 + 18 + (U[0] - bP) * 46 + -12, 0, 0, 0);
         } else {
            u[0].a((Graphics)var0, 28, 0, 120, var9 + 18 + 18 + (U[0] - bP) * 46 + -12, 0, 0, 0);
         }

         b();
         boolean var10 = false;
         int var10002;
         if (a(var8 + var4 / 2 - 30, var9 - 22, 60, 30, true)) {
            u[0].a((Graphics)var0, 29, 2, 120, var9 - 22, 0, 0, 0);
            if (U[0] > 0) {
               var10002 = U[0]--;
               bP -= U[0] >= bP ? 0 : 1;
            } else {
               U[0] = U[1] - 1;
               bP = U[1] - bQ;
               if (bP < 0) {
                  bP = 0;
               }
            }
         } else {
            u[0].a((Graphics)var0, 29, 1, 120, var9 - 22, 0, 0, 0);
         }

         if (a(var8 + var4 / 2 - 30, var9 + var7 + 22 - 30, 60, 30, true)) {
            u[0].a((Graphics)var0, 29, 2, 120, var9 + var7 + 22, 2, 0, 0);
            if (U[0] < U[1] - 1) {
               var10002 = U[0]++;
               bP += U[0] >= bP + bQ ? 1 : 0;
            } else {
               U[0] = 0;
               bP = 0;
            }
         } else {
            u[0].a((Graphics)var0, 29, 1, 120, var9 + var7 + 22, 2, 0, 0);
         }

         int var15;
         if ((var15 = bP + bQ) > U[1]) {
            var15 = U[1];
         }

         for(var3 = bP; var3 < var15; ++var3) {
            int var12 = var9 + 18 + 18 + (var3 - bP) * 45 + 5;
            String var13 = GloftMMN.k(R[var3]);
            if (T[var3] != null) {
               var13 = var13 + T[var3];
            }

            if (var3 != U[0]) {
               u[0].a((Graphics)var0, 28, 1, 120, var9 + 18 + 18 + (var3 - bP) * 46 + -12, 0, 0, 0);
            }

            M.a(var0, var13, 120, var12 + 1, 3, 1);
            a(20, var12 + 1 - 20, 200, 45, 0, 0, 9, var3, 8, var3);
            if (S[var3] != -1 && (S[var3] != 26 || aG % 4L < 2L)) {
               u[1].a((Graphics)var0, S[var3], 0, 22, var12 + -7 - 2, 0, 0, 0);
            }
         }

         if (h == 8 && (y == 32 || y == 73)) {
            var0.setColor(0);
            var0.fillRect(0, 340, 240, 60);
         }

      }
   }

   private static void a(boolean var0) {
      if (var0 && aP && U[4] == -1) {
         z = (byte)U[5];
      }

      int var10002;
      if (aC[1] < 0) {
         if (U[0] > 0) {
            var10002 = U[0]--;
            bP -= U[0] >= bP ? 0 : 1;
         } else {
            U[0] = U[1] - 1;
            bP = U[1] - bQ;
            if (bP < 0) {
               bP = 0;
            }
         }
      }

      if (aC[2] < 0) {
         if (U[0] < U[1] - 1) {
            var10002 = U[0]++;
            bP += U[0] >= bP + bQ ? 1 : 0;
            return;
         }

         U[0] = 0;
         bP = 0;
      }

   }

   private static void a(short var0, byte[] var1, int var2) {
      var1[var2] = (byte)(var0 >>> 8);
      var1[var2 + 1] = (byte)(var0 & 255);
   }

   private static void a(int var0, byte[] var1, int var2) {
      var1[var2] = (byte)(var0 >>> 24);
      var1[var2 + 1] = (byte)(var0 >>> 16);
      var1[var2 + 2] = (byte)(var0 >>> 8);
      var1[var2 + 3] = (byte)(var0 & 255);
   }

   static int a(byte[] var0, int var1) {
      return (var0[var1] & 255) << 24 | (var0[var1 + 1] & 255) << 16 | (var0[var1 + 2] & 255) << 8 | var0[var1 + 3] & 255;
   }

   private static void a(long var0, byte[] var2, int var3) {
      var2[var3] = (byte)((int)(var0 >>> 56));
      var2[var3 + 1] = (byte)((int)(var0 >>> 48));
      var2[var3 + 2] = (byte)((int)(var0 >>> 40));
      var2[var3 + 3] = (byte)((int)(var0 >>> 32));
      var2[var3 + 4] = (byte)((int)(var0 >>> 24));
      var2[var3 + 5] = (byte)((int)(var0 >>> 16));
      var2[var3 + 6] = (byte)((int)(var0 >>> 8));
      var2[var3 + 7] = (byte)((int)(var0 & 255L));
   }

   static long b(byte[] var0, int var1) {
      return (long)(var0[var1 + 0] & 255) << 56 | (long)(var0[var1 + 1] & 255) << 48 | (long)(var0[var1 + 2] & 255) << 40 | (long)(var0[var1 + 3] & 255) << 32 | (long)(var0[var1 + 4] & 255) << 24 | (long)(var0[var1 + 5] & 255) << 16 | (long)(var0[var1 + 6] & 255) << 8 | (long)(var0[var1 + 7] & 255);
   }

   private static boolean a(boolean var0, boolean var1) {
      RecordStore var3 = null;
      RecordEnumeration var4 = null;

      try {
         var4 = (var3 = RecordStore.openRecordStore("Miami Nights" + 1, true)).enumerateRecords((RecordFilter)null, (RecordComparator)null, false);
         int var5;
         if (!var1 && var0) {
            av[0] = ag;
            if (var4.hasNextElement()) {
               var5 = var4.nextRecordId();
               var3.setRecord(var5, av, 0, av.length);
            } else {
               var3.addRecord(av, 0, av.length);
            }

            return true;
         }

         try {
            var5 = var4.nextRecordId();
            boolean var7;
            byte[] var24;
            if ((var24 = var3.getRecord(var5)).length < 1) {
               var7 = false;
               return false;
            }

            System.arraycopy(var24, 0, av, 0, var24.length);
            ag = av[0];
            ax = 0;
            if (ag > 1) {
               ax = 1;
            }

            if (ag == 2) {
               ag = 0;
            }

            System.out.println("IGP info :: " + ag + " s_vibrateEnable::" + ax);
            if (!var1) {
               return true;
            }

            var7 = true;
            return var7;
         } catch (InvalidRecordIDException var21) {
            var4.reset();
            boolean var6 = false;
         }
      } catch (Exception var22) {
         return false;
      } finally {
         if (var4 != null) {
            var4.destroy();
         }

         if (var3 != null) {
            try {
               var3.closeRecordStore();
            } catch (RecordStoreException var20) {
            }
         }

         System.gc();
      }

      return false;
   }

   private static boolean a(boolean var0, boolean var1, boolean var2) {
      RecordStore var5 = null;
      RecordEnumeration var6 = null;

      boolean var9;
      try {
         if (aw == null) {
            aw = new byte[3982];
         }

         var6 = (var5 = RecordStore.openRecordStore("Miami Nights" + 0, true)).enumerateRecords((RecordFilter)null, (RecordComparator)null, false);
         if (!var1 && var0) {
            g var30 = s[0];
            aw[0] = ax;
            aw[1] = ai;

            int var3;
            for(var3 = 0; var3 < g.D; ++var3) {
               aw[2 + var3] = (byte)var30.a[var3];
            }

            a((int)var30.c[0], aw, 49);
            a((int)var30.c[1], aw, 53);
            a((int)var30.a[35], aw, 57);
            aw[61] = (byte)H;
            a(f.aT, aw, 62);

            for(var3 = 0; var3 < 32; ++var3) {
               aw[70 + var3] = f.bc[var3];
            }

            for(var3 = 0; var3 < 16; ++var3) {
               aw[102 + var3] = f.bb[var3];
            }

            for(var3 = 0; var3 < 39; ++var3) {
               aw[118 + var3] = g.ah[var3];
               aw[157 + var3] = g.f[var3];
            }

            for(var3 = 0; var3 < 39; ++var3) {
               aw[196 + var3] = (byte)(g.ai[var3] ? 1 : 0);
            }

            for(var3 = 0; var3 < 16; ++var3) {
               a(g.ag[var3], aw, 235 + var3 * 8);
            }

            for(var3 = 0; var3 < 46; ++var3) {
               a((short)((int)g.q[var3]), aw, 363 + var3 * 2);
            }

            for(var3 = 0; var3 < var30.an.length; ++var3) {
               aw[455 + var3] = var30.an[var3];
            }

            for(var3 = 0; var3 < 64; ++var3) {
               if (var3 < f.ap.length) {
                  a(f.ap[var3], aw, 495 + var3 * 8);
               }
            }

            for(var3 = 0; var3 < 25; ++var3) {
               aw[1007 + var3] = (byte)g.G[var3];
            }

            for(var3 = 0; var3 < 168; ++var3) {
               aw[1032 + var3] = (byte)g.I[var3];
            }

            a((int)g.ae, aw, 1200);

            for(var3 = 0; var3 < 200; ++var3) {
               if (var3 < g.ad.length()) {
                  aw[1204 + var3] = (byte)g.ad.charAt(var3);
               } else {
                  aw[1204 + var3] = 0;
               }
            }

            aw[1404] = g.aa;

            for(var3 = 0; var3 < 252; ++var3) {
               aw[1405 + var3] = e[var3];
            }

            for(var3 = 0; var3 < 64; ++var3) {
               aw[1657 + var3] = f[var3];
            }

            for(var3 = 0; var3 < 16; ++var3) {
               aw[1721 + var3] = bM[var3];
            }

            for(var3 = 0; var3 < 10; ++var3) {
               if (var3 < af.length()) {
                  aw[1737 + var3] = (byte)af.charAt(var3);
               } else {
                  aw[1737 + var3] = -127;
               }
            }

            int var32 = 0;

            for(var3 = 1; var3 < s.length; ++var3) {
               g var33 = s[var3];
               int var10 = 1747 + var32 * 89;
               if (var33 != null && var33.e != null) {
                  if (var32 >= 25) {
                     return false;
                  }

                  aw[var10 + 0] = (byte)var3;
                  int var11;
                  if (var33.k != null) {
                     for(var11 = 0; var11 < 6; ++var11) {
                        if (var11 < var33.k.length) {
                           aw[var10 + 1 + var11] = var33.k[var11];
                        }
                     }
                  }

                  if (var33.l != null) {
                     for(var11 = 0; var11 < 6; ++var11) {
                        if (var11 < var33.l.length) {
                           aw[var10 + 7 + var11] = var33.l[var11];
                        }
                     }
                  }

                  if (var33.m != null) {
                     for(var11 = 0; var11 < 6; ++var11) {
                        if (var11 < var33.m.length) {
                           aw[var10 + 13 + var11] = var33.m[var11];
                        }
                     }
                  }

                  for(var11 = 0; var11 < 46; ++var11) {
                     aw[var10 + 19 + var11] = (byte)var33.a[var11];
                     if (var11 == 1) {
                        byte[] var10000 = aw;
                        var10000[var10 + 19 + var11] = (byte)(var10000[var10 + 19 + var11] - 402);
                     }
                  }

                  for(var11 = 0; var11 < 21; ++var11) {
                     aw[var10 + 65 + var11] = var33.b[var11];
                  }

                  for(var11 = 0; var11 < 3; ++var11) {
                     aw[var10 + 86 + var11] = var33.e[var11];
                  }

                  ++var32;
               }
            }

            if (var6.hasNextElement()) {
               int var34 = var6.nextRecordId();
               var5.setRecord(var34, aw, 0, aw.length);
            } else {
               var5.addRecord(aw, 0, aw.length);
            }

            return true;
         }

         try {
            int var7 = var6.nextRecordId();
            byte[] var31;
            if ((var31 = var5.getRecord(var7)).length >= 3982) {
               System.arraycopy(var31, 0, aw, 0, var31.length);
               if (var1) {
                  var9 = true;
                  return var9;
               }

               ax = aw[0];
               ai = aw[1];
               if (var2) {
                  var9 = true;
                  return var9;
               }

               H = aw[61];
               f.ah = true;
               return true;
            }

            var9 = false;
         } catch (InvalidRecordIDException var27) {
            var6.reset();
            boolean var8 = false;
            return false;
         }
      } catch (Exception var28) {
         return false;
      } finally {
         if (var6 != null) {
            var6.destroy();
         }

         if (var5 != null) {
            try {
               var5.closeRecordStore();
            } catch (RecordStoreException var26) {
            }
         }

         System.gc();
      }

      return var9;
   }

   private static void a(Graphics var0) {
      int var1 = u[0].b(u[0].d(28, 1)) / 2 - 5;
      b();
      a(20, 224 - var1, 200, 40, 6, 0, 3, 0, 6, 0);
      a(20, 269 - var1, 200, 40, 7, 0, 3, 0, 7, 0);
      if (aC[3] < 0) {
         i = 1;
      }

      if (aC[4] < 0) {
         i = 0;
      }

      if (h == 16) {
         var0.setColor(0);
         var0.fillRect(0, 318, 240, 82);
      }

      g.a(var0, 3, 151, 233, 163, 16777215, 0, 0);
      var0.setColor(296700);
      var0.fillRect(4, 152, 231, 18);
      var0.setColor(16777215);
      if (l) {
         i = 0;
      }

      if (i == 1) {
         var0.setColor(296700);
      }

      if (i == 0) {
         var0.setColor(296700);
      } else {
         var0.setColor(16777215);
      }

      var0.setColor(0);
      var0.fillRect(3, 170, 233, 1);
      short var2 = 835;
      short var3 = 276;
      short var4 = 275;
      if (6 == h) {
         var2 = 46;
         var3 = 16;
         var4 = 17;
      }

      M.a(var0, GloftMMN.k(var2), 120, 152, 17, 0);
      if (g.ak != null) {
         String var5 = g.b(g.ak, 213);
         M.a(var0, var5, 120, 171, 17, 1);
      }

      M.a(GloftMMN.k(var3));
      int var8 = a.y;
      M.a(GloftMMN.k(var4));
      int var6 = a.y;
      if (i == 1) {
         if (a(20, 224 - var1, 200, 40)) {
            u[0].a((Graphics)var0, 28, 2, 120, 224 - var1, 0, 0, 0);
         } else {
            u[0].a((Graphics)var0, 28, 0, 120, 224 - var1, 0, 0, 0);
         }

         u[0].a((Graphics)var0, 28, 1, 120, 269 - var1, 0, 0, 0);
      } else if (i == 0) {
         u[0].a((Graphics)var0, 28, 1, 120, 224 - var1, 0, 0, 0);
         if (a(20, 269 - var1, 200, 40)) {
            u[0].a((Graphics)var0, 28, 2, 120, 269 - var1, 0, 0, 0);
         } else {
            u[0].a((Graphics)var0, 28, 0, 120, 269 - var1, 0, 0, 0);
         }
      }

      M.a(var0, GloftMMN.k(var3), 240 - var8 >> 1, 224, 20, 1);
      M.a(var0, GloftMMN.k(var4), 240 - var6 >> 1, 269, 20, 1);
      if (aP && 6 != h) {
         g.a(g.al & 255, i == 1);
         g.ak = null;
      }

      if (h != 6) {
         var0.setColor(0);
         var0.fillRect(0, 340, 240, 60);
      }

   }

   private void e() {
      aB.translate(0, 0);
      boolean var2;
      int var5;
      d var6;
      d var7;
      d var8;
      boolean var10;
      boolean var11;
      int var12;
      int var14;
      int var15;
      int[] var10000;
      boolean var16;
      int var17;
      int var10002;
      d var18;
      int var19;
      int var20;
      int var21;
      int var22;
      a var23;
      int var26;
      int var27;
      int var32;
      int var33;
      int var34;
      byte[] var46;
      int var56;
      g var57;
      int var60;
      d var62;
      d var68;
      boolean var71;
      int var73;
      d var74;
      d var75;
      int var77;
      boolean var78;
      int var80;
      boolean var90;
      boolean var93;
      int var95;
      byte var106;
      switch(h) {
      case 0:
         a(100);
         return;
      case 3:
         aB.setColor(0);
         aB.fillRect(0, 0, 240, 400);
         M.a(aB, GloftMMN.k(0), 120, 200, 3, 0);
         M.a(aB, GloftMMN.k(17), 237, 398, 40, 0);
         M.a(aB, GloftMMN.k(16), 3, 398, 36, 0);
         if (aC[13] < 0) {
            GloftMMN.f = false;
            a(5);
            i = 0;
         }

         if (aC[14] < 0) {
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
            GloftMMN.f = true;
            a(5);
            i = 0;
            return;
         }
         break;
      case 4:
         if (k) {
            i = 0;
         }

         label3079:
         switch(i) {
         case 0:
            s = null;
            o = null;
            n = null;
            p = (a[][])null;
            u = null;
            M = null;
            t = null;
            g.T = null;
            g.S = null;
            g.U = new Image[4];
            g.Z = null;
            GloftMMN.a("STARTING GAME INIT");
            g.C = 10;
            GloftMMN.a("AFTER LOADING CHARACTER CLASS");
            f.a = -1;
            GloftMMN.a("AFTER LOADING LEVEL CLASS");
            o = new a[2];
            GloftMMN.a("AFTER LOADING ASPRITE CLASS");
            n = new d[2];
            n = null;
            GloftMMN.a("AFTER LOADING ASPRITE INSTANCE CLASS");
            aB.setColor(16777215);
            aB.fillRect(0, 0, 240, 400);
            GloftMMN.b("/0");
            Q = Image.createImage(var46 = GloftMMN.j(0), 0, var46.length);
            if (!Z) {
               aB.drawImage(Q, 120, 200, 3);
            }
            break;
         case 1:
            f.a = -1;
            K = 0;
            int var52 = 0;

            while(true) {
               if (var52 >= N.length) {
                  break label3079;
               }

               N[var52] = 0;
               ++var52;
            }
         case 2:
            Q = Image.createImage(var46 = GloftMMN.j(1), 0, var46.length);
            GloftMMN.a("AFTER LOADING SPLASH");
            GloftMMN.a("AFTER LOADING IFACE DATA");
            break;
         case 3:
            f.b(true);
            break;
         case 4:
            f.a();
            GloftMMN.a("AFTER LOADING FONT");
            GloftMMN.a("/3", 1);
            GloftMMN.f();
            GloftMMN.a("AFTER LOADING TEXTS MENU");
            GloftMMN.b("/20");
            GloftMMN.a("AFTER OPENING PACK ARRAYS");
            g.f = GloftMMN.f(0);
            GloftMMN.a("AFTER m_bScenesMapVisibility");
            a = GloftMMN.g(1);
            GloftMMN.a("AFTER armBodyCombinations");
            b = new byte[2][][];
            b[0] = GloftMMN.g(2);
            b[1] = GloftMMN.g(3);
            b[1] = GloftMMN.g(3);
            GloftMMN.a("AFTER clothesPrice");
            c = GloftMMN.f(4);
            GloftMMN.a("AFTER m_bShopAnimList");
            g = GloftMMN.e(5);
            GloftMMN.a("AFTER m_sBuyableObjectsInfo");
            GloftMMN.f();
            GloftMMN.a("AFTER COLOSING PACK ARRAYS");
            g.j = new byte[2];
            break;
         case 5:
            GloftMMN.a();
            GloftMMN.b("/18");
            GloftMMN.a(0);
            GloftMMN.a(1);
            GloftMMN.a(2);
            GloftMMN.a(3);
            GloftMMN.a(4);
            GloftMMN.f();
            GloftMMN.a("AFTER INITIAL SOUNDS");
            i = -1;
            if (ay < 0) {
               String var47;
               if ((var47 = System.getProperty("microedition.locale").toLowerCase()).startsWith("en")) {
                  ay = 0;
               } else if (var47.startsWith("fr")) {
                  ay = 1;
               } else if (var47.startsWith("de")) {
                  ay = 2;
               } else if (var47.startsWith("it")) {
                  ay = 3;
               } else if (var47.startsWith("es")) {
                  ay = 4;
               } else if (var47.startsWith("pt")) {
                  ay = 0;
               } else if (var47.startsWith("pl")) {
                  ay = 5;
               } else {
                  ay = 0;
               }
            } else {
               GloftMMN.b("/19");
               GloftMMN.a(20);
               GloftMMN.f();
               GloftMMN.c(5);
               a(5);
            }

            long var50 = System.currentTimeMillis();

            while(true) {
               if (System.currentTimeMillis() - var50 >= 2000L) {
                  if (Z) {
                     a(6);
                     Z = false;
                  }
                  break;
               }
            }
         }

         if (Z) {
            aB.setClip(0, 0, 240, 400);
            aB.setColor(0);
            aB.fillRect(0, 0, 240, 400);
            aB.setClip(0, 0, 231 * i / 4, 400);
            aB.setColor(16711680);
            aB.fillRect(7, 316, 240, 3);
         }

         ++i;
         return;
      case 5:
         if (i == 0) {
            GloftMMN.b("/19");
            GloftMMN.a(20);
            GloftMMN.f();
            GloftMMN.c(5);
            i = 1;
         }

         aB.drawImage(Q, 120, 200, 3);
         if (aG % 4L < 2L) {
            M.a(aB, GloftMMN.k(2), 121, 351, 3, 1);
            M.a(aB, GloftMMN.k(2), 120, 350, 3, 0);
         }

         if (aP) {
            bB = -1;
            bC = -1;
            i = -1;
            a(6);
         }

         b();
         b(0, 0, 240, 400, 9, 0);
         if (bZ && GloftMMN.f) {
            aB.setClip(0, 0, 240, 400);
            aB.clipRect(0, 0, 240, 400);
            if (bY) {
               --bW;
               if (bW < 2) {
                  bY = false;
               }
            } else {
               ++bW;
               if (bW > bV) {
                  bZ = false;
               }
            }

            aB.setColor(255, 255, 255);
            aB.fillRoundRect(2, bX + 1 + bW, bU - 2, bV + bW - 2, 2, 2);
            aB.setColor(16758678);
            aB.fillRoundRect(3, bX + 2 + bW, bU - 4, bV + bW - 4, 2, 2);
            M.a(aB, GloftMMN.k(60), 65, bX + 4 + bW, 5, 1);
            M.a(aB, GloftMMN.k(61), 70, bX + 4 + 15 + bW, 5, 1);
            M.a(aB, GloftMMN.k(62), 93, bX + 4 + 30 + bW, 5, 1);
            return;
         }
         break;
      case 6:
         if (k) {
            i = -1;
         }

         if (g.ak != null) {
            if (k) {
               aB.drawImage(Q, 120, 200, 3);
               a(aB, false, false);
            }

            a(aB);
            if (aP) {
               if (i == 0) {
                  g.ak = null;
                  i = -1;
                  return;
               }

               if (i == 1) {
                  h = -1;
                  return;
               }
            }

            return;
         }

         if (i == -1) {
            aB.drawImage(Q, 120, 200, 3);
            boolean var53;
            if (aw == null) {
               var53 = a(false, true, true);
            } else {
               var53 = a(false, true, false);
            }

            i = 7;
            if (!var53) {
               --i;
            }

            if (!c.a()) {
               --i;
            }

            a(i, 0, false);
            var2 = a(false, true);
            int var48 = 0;
            int var4 = 5;

            while(true) {
               if (var4 > 13) {
                  U[2] = 14;
                  U[3] = 15;
                  U[5] = -1;
                  i = 0;
                  break;
               }

               if (var4 != 8 && var4 != 9 && (var53 && var4 == 6 || var4 >= 10 || var4 == 5 || var4 == 7 && c.a()) && (var4 != 8 || c.a())) {
                  R[var48] = var4;
                  if (var4 == 7 && (!var2 || ag == 0)) {
                     S[var48] = 26;
                  }

                  ++var48;
               }

               ++var4;
            }
         }

         a(false);
         a(aB, false, false);
         int var55;
         if (aP) {
            var55 = U[0];
            if ((c.a() && U[1] == 6 || !c.a() && U[1] == 5) && var55 > 0) {
               ++var55;
            }

            if (!c.a() && var55 > 1) {
               ++var55;
            }

            switch(var55) {
            case 0:
               GloftMMN.b();
               a(7);
               GloftMMN.b(5);
               break;
            case 1:
               f.ah = true;
               GloftMMN.b();
               a(7);
               GloftMMN.b(5);
               break;
            case 2:
               if (ax == 0) {
                  ag = 1;
               } else {
                  ag = 3;
               }

               a(true, false);
               i = 0;
               y = 69;
               a(24);
               break;
            case 3:
               d(31, 1);
               a(19);
               break;
            case 4:
               a(22);
               i = -1;
               break;
            case 5:
               a(21);
               i = -1;
               break;
            case 6:
               g.a(GloftMMN.k(45), (byte)40);
            }
         }

         if (!ao) {
            if (aC[ap] < 0) {
               switch(ap) {
               case 6:
                  ap = 7;
                  break;
               case 7:
                  ap = 9;
                  break;
               case 8:
                  ap = 10;
                  break;
               case 9:
                  ap = 8;
                  break;
               case 10:
                  ap = 6;
                  ao = true;
               }

               return;
            }

            for(var55 = 0; var55 < aC.length; ++var55) {
               if (aC[var55] < 0) {
                  ap = 6;
                  return;
               }
            }
         }
         break;
      case 7:
         if (k) {
            aB.setClip(0, 0, 240, 400);
            aB.setColor(0);
            aB.fillRect(0, 0, 240, 400);
         }

         try {
            aB.setClip(0, 0, 240, 400);
            if (i == 0) {
               X = 1;
               GloftMMN.a("GAME_INIT START");
               aB.setClip(0, 0, 240, 800);
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 800);
            }

            if (i == 1) {
               GloftMMN.a("/3", 0);
               GloftMMN.a("GAME_INIT TEXTS GAME");
               g.a(GloftMMN.k(!f.ah ? 752 : 753), (int)2);
               if (f.ah) {
                  g.ae &= -257;
               }

               GloftMMN.b("/1");
               byte[] var66 = GloftMMN.j(1);
               GloftMMN.f();
               Q = Image.createImage(var66, 0, var66.length);
               GloftMMN.a("GAME_INIT LOADING IMG");
               (var75 = v[7]).a = 30720;
               var75.b = 51200;
               var75.a(47);
               var75.a(aB);
            }

            if (i == 2) {
               f.n = 0;
               n = new d[28];
               t = new d[1];
               s = new g[28];
               GloftMMN.a("GAME_INIT MISC ARRAYS");
            }

            if (i == 3) {
               GloftMMN.l = new Random(System.currentTimeMillis());
               GloftMMN.a("GAME_INIT RANDOM");
            }

            if (i == 4) {
               f.n = 284;
               f.o = 284;
               GloftMMN.a("GAME_INIT BB");
            }

            if (i == 5) {
               GloftMMN.a("GAME_INIT BEFORE SCRIPTING");
               g.d();
               GloftMMN.a("GAME_INIT (SCRIPTING) AFTER ATTRIBUTES");
               f.f();
               GloftMMN.a("GAME_INIT (SCRIPTING) AFTER NPC ACTIONS");
               f.h();
               GloftMMN.a("GAME_INIT (SCRIPTING) AFTER NPC TEMPLATES");
               f.g();
               GloftMMN.a("GAME_INIT (SCRIPTING) AFTER NPC OCCUPATIONS");
               g.j();
               GloftMMN.a("GAME_INIT (SCRIPTING) AFTER CONDITIONS");
               g.k();
               GloftMMN.a("GAME_INIT (SCRIPTING) AFTER REACTIONS");
               GloftMMN.a("GAME_INIT AFTER SCRIPTING");
            }

            if (i == 6) {
               q = new byte[2][];
               r = new byte[2][];
               int var61 = 7;

               while(true) {
                  if (var61 > 9) {
                     GloftMMN.f();
                     g.a(false);
                     s[0] = new g(9, 0, 0, false, false);
                     GloftMMN.f();
                     break;
                  }

                  var56 = (var61 - 7) / 2;
                  a(var61 + 0, o, var56, false, false, -1, false);
                  q[var56] = GloftMMN.j(var61 + 1);
                  r[var56] = new byte[6];

                  for(var60 = 0; var60 < q[var56].length; ++var60) {
                     byte var67;
                     if ((var67 = q[var56][var60]) >= 0) {
                        ++r[var56][var67];
                     }
                  }

                  var61 += 2;
               }
            }

            if (i == 7) {
               f.a(false);
               f.bb = new byte[16];
               f.bc = GloftMMN.j(6);
               GloftMMN.f();
            }

            if (i == 8) {
               X = 0;
               f.p();
               f.b(true);
               if (f.ah) {
                  a(8);
                  System.out.println("RRRRRRRRRRRRRRMMMMMMMMMMMMMMMMMMSSSSSSSSSSSSSSSSSSSs");
                  a(false, false, false);
                  y = 20;
                  L = 0;
                  z = -1;
               } else {
                  a(17);
                  at = false;
                  i = -2;
               }
            }
         } catch (Exception var43) {
            var43.toString();
         }

         if ((var56 = i) < 0) {
            var56 = 7;
         }

         if (f.ah) {
            aB.setClip(0, 0, 231 * var56 / 30, 400);
         } else {
            aB.setClip(0, 0, 231 * var56 / 7, 400);
         }

         aB.setColor(16711680);
         aB.fillRect(7, 316, 240, 3);
         ++i;
         return;
      case 8:
         var62 = n[0];
         g var72 = s[0];
         if (!f.d && (y == 0 || y == 19)) {
            if (N[0] >= 0) {
               f.au = N[0];
               f.f(f.au);
               B = (byte)N[1];
               f.a(true, true);
               f.a(f.aX[B], true);
               y = z;
               z = -1;
               N[0] = -1;
               f.a(aB, true, true);
            }

            if (N[4] > 0) {
               n[0].a(N[4]);
            }
         }

         if (y == 0 || y == 5) {
            if (N[3] == 0) {
               f.aT += aH * f.aU / 100L;
               if (y == 5 && f.aT > f.aB) {
                  f.aT = f.aB;
               }

               var72.f();
               g.b(false);
            }

            if (X == 1) {
               var10002 = N[6]++;
               if (N[6] >= N[5]) {
                  N[4] = -1;
                  X = 2;
                  r();
                  H = 1;
                  L = 0;
                  y = 20;
                  Y = true;
                  z = -1;
               } else {
                  g.d(N[6]);
                  if (!f.d && g.N.size() > 0) {
                     y = 17;
                  }
               }
            }
         }

         byte var87;
         switch(y) {
         case 0:
            label4337: {
               if (ao && aC[6] < 0) {
                  ++ak;
                  ak %= 4;
                  if (ak == 2) {
                     al = 0;
                     an = new int[s[0].a.length];
                     am = 0;

                     for(var5 = 0; var5 < s[0].a.length; ++var5) {
                        if ((g.d(var5, 1) & 1) != 1 || var5 == 35) {
                           an[am++] = var5;
                        }
                     }
                  }
               }

               g var98;
               if (y == 0 && z < 0) {
                  switch(ak) {
                  case 0:
                  default:
                     break;
                  case 1:
                     if (aP) {
                        ak = 0;

                        for(var5 = 353; var5 < 400; ++var5) {
                           g.a(var5);
                        }

                        for(var5 = 498; var5 < 780; ++var5) {
                           g.a(var5);
                        }
                     }
                     break;
                  case 2:
                     if (aC[3] < 0) {
                        --al;
                        if (al < 0) {
                           al = am - 1;
                        }
                     } else if (aC[4] < 0) {
                        ++al;
                        al %= am;
                     } else if (aC[2] > 0) {
                        s[0].c(an[al], -1);
                     } else if (aC[1] > 0) {
                        s[0].c(an[al], 1);
                     }
                     break;
                  case 3:
                     if (f.av != -1) {
                        var98 = s[f.av];
                        if (aC[2] > 0) {
                           if (var98.a[39] > 1) {
                              var10002 = var98.a[39]--;
                           }
                        } else if (aC[1] > 0 && var98.a[39] < 100) {
                           var10002 = var98.a[39]++;
                        }
                     }
                  }
               }

               if (N[4] <= 0 && ak == 0) {
                  g.p += aH;
                  if (aC[7] <= 0 && aC[1] <= 0 && aC[8] <= 0 && aC[3] <= 0 && aC[4] <= 0 && aC[9] <= 0 && aC[2] <= 0 && aC[10] <= 0) {
                     if (bp <= bq && bo != null) {
                        if ((var77 = (int)aH) > 200) {
                           var77 = 200;
                        }

                        var12 = 0;
                        var73 = 0;
                        var14 = var72.c[0];
                        var15 = var72.c[1];
                        var80 = f(bo[bq] % bs);
                        var17 = f(bo[bq] / bs);
                        var95 = var80 - var14;
                        var19 = var17 - var15;
                        var95 = var95 * 1000 / var77;

                        for(var19 = var19 * 1000 / var77; bq >= 0; --bq) {
                           var80 = f(bo[bq] % bs);
                           var17 = f(bo[bq] / bs);
                           var95 = var80 - var72.c[0];
                           var19 = var17 - var72.c[1];
                           var95 = var95 * 1000 / var77;
                           var19 = var19 * 1000 / var77;
                           if (!a((long)var95, (long)var19)) {
                              c(var12, var73, var95, var19);
                              var95 = bw;
                              var19 = bx;
                              break;
                           }

                           var14 = var80;
                           var15 = var17;
                           var12 = var95;
                           var73 = var19;
                        }

                        var93 = false;
                        boolean var117 = false;
                        byte var112;
                        if (var95 > 0) {
                           if (Math.abs(var95) >= Math.abs(var19)) {
                              var112 = 4;
                              var106 = 0;
                           } else if (var19 < 0) {
                              var106 = 0;
                              var112 = 6;
                           } else {
                              var106 = 1;
                              var112 = 4;
                           }
                        } else if (Math.abs(var95) > Math.abs(var19)) {
                           var112 = 6;
                           var106 = 1;
                        } else if (var19 < 0) {
                           var106 = 0;
                           var112 = 6;
                        } else {
                           var106 = 1;
                           var112 = 4;
                        }

                        if (!var72.a(var95, var19, var112, var106, false) && var14 != var72.c[0] && var15 != var72.c[1]) {
                           var72.a(var14, var15, var112, var106, true);
                        }

                        if (aR) {
                           bp = 1;
                           bq = 0;
                        }
                     }
                  } else {
                     a(var72, var62, aC[3] > 0 || aC[7] > 0 || aC[9] > 0, aC[4] > 0 || aC[8] > 0 || aC[10] > 0, aC[1] > 0 || aC[7] > 0 || aC[8] > 0, aC[2] > 0 || aC[9] > 0 || aC[10] > 0);
                     bp = 1;
                     bq = 0;
                  }
               }

               g.m();
               if (aP) {
                  D = 0;
                  if (f.av >= 0) {
                     var98 = s[f.av];
                     if ((var87 = f.bc[25]) > 0 && var98.b[6] == var87 && f.bc[24] != H) {
                        g.a((int)780);
                        g.au = true;
                     } else {
                        if (var98.b[7] == -1) {
                           var98.b[7] = (byte)n[var98.b[5]].c;
                        }

                        g.b(f.av);
                        if (!g.au) {
                           f.a(aB, true);
                        }
                     }
                  } else if (f.au < 0 && f.aR >= 0) {
                     short var102;
                     if ((var102 = f.e[f.aR][4]) >= 0) {
                        a(2, 0, true);
                        R[0] = 141 + var102;
                        R[1] = 400;
                     } else if (var102 != -2) {
                        i = -1;
                        b();
                        a(15);
                        break label4337;
                     }

                     if (var102 >= 0) {
                        i = 0;
                        U[2] = 401;
                        U[3] = 781;
                        U[5] = 21;
                        z = 22;
                     }
                  }
               }

               if ((aC[13] < 0 || (g.ae & 256) == 256 && f.aT - g.af >= 5000L) && !aP) {
                  GloftMMN.c(3);
                  a(16);
                  y = 24;
                  g.ae &= -257;
                  K = 0;
               } else {
                  if (aC[14] < 0 || k && K > 1) {
                     y = 32;
                     if (k) {
                        i = -2;
                     } else {
                        i = -1;
                     }
                  }

                  if (g.p > 0L) {
                     var11 = g.g(var62.e);
                     if (g.v == 85) {
                        var62.a(var11 ? 88 : 87);
                     } else if (g.v == 91) {
                        var62.a(var11 ? 94 : 93);
                     } else {
                        var71 = g.v != 0;
                        byte var103 = 0;
                        if (var71) {
                           var103 = 8;
                        }

                        if (!var71 && g.p >= 6000L) {
                           if (g.p < 7000L && var62.a()) {
                              var62.a(3);
                           } else if (g.p >= 7000L && var62.a()) {
                              if ((var14 = GloftMMN.c(0, 100)) < 25) {
                                 var62.a(3);
                              } else if (var14 < 50) {
                                 var62.a(2);
                              } else {
                                 var62.a(0);
                              }
                           }
                        } else if (g.g(var62.e)) {
                           var62.a(1 + var103);
                        } else {
                           var62.a(0 + var103);
                        }
                     }
                  }

                  if (z == -1) {
                     if (g.v <= 6) {
                        if (!f.f(var72.c[0], var72.c[1])) {
                           f.e(var72.c[0], var72.c[1]);
                        }
                     } else {
                        f.av = -1;
                        f.au = -1;
                     }
                  }

                  if (K > 0 && !f.d && g.N.size() > 0) {
                     z = 17;
                  } else if (g.ak != null) {
                     i = 0;
                     z = 33;
                  }

                  if (y == 0 && z == -1) {
                     if (((var77 = f.b(var72.c[0], var72.c[1])) & 16384) != 0) {
                        if (g.v <= 6) {
                           g.w = g.v;
                        }

                        g.v = 85;
                     } else if ((var77 & -32768) != 0) {
                        if (g.v <= 6) {
                           g.w = g.v;
                        }

                        g.v = 91;
                     } else {
                        g.v = g.w;
                     }
                  }

                  if (aj && ai != 0 && K > 0 && !k) {
                     z = 76;
                     aj = false;
                  }
               }
            }
         case 1:
         case 6:
         case 7:
         case 8:
         case 9:
         case 18:
         case 19:
         case 20:
         case 21:
         case 23:
         case 24:
         case 25:
         case 26:
         case 27:
         case 28:
         case 29:
         case 30:
         case 31:
         case 33:
         case 34:
         case 36:
         case 37:
         case 38:
         case 39:
         case 40:
         case 41:
         case 42:
         case 43:
         case 44:
         case 45:
         case 46:
         case 47:
         case 48:
         case 49:
         case 50:
         case 51:
         case 52:
         case 53:
         case 54:
         case 55:
         case 56:
         case 57:
         case 58:
         case 59:
         case 60:
         case 61:
         case 62:
         case 63:
         case 64:
         case 65:
         case 66:
         default:
            break;
         case 2:
            if (K == 0) {
               f.a(aB, false, true);
            }

            if (!f.a(false, false)) {
               break;
            }

            var77 = f.i(f.aX[B], 5);
            var12 = f.ao[f.g(var77, -1)];
            if (f.a(s[0], f.an, var12 + 6, false, true) > var72.a[35]) {
               g.a((int)434);
               System.out.println("this is correct one....");
               z = 0;
            } else {
               var80 = f.g(var15 = f.i(var14 = f.aX[B], 5), -1);
               var17 = f.g(var15, 4);
               long var97;
               long var110 = (var97 = f.ap[var80]) + (long)var17 * 60L * 1000L / 60L;
               if (!g.e(var15)) {
                  z = 0;
               } else {
                  if (var97 != 0L && f.aT < var110) {
                     f.a(var14, false);
                     break;
                  }

                  f.a(var14, true);
               }
            }
            break;
         case 3:
         case 4:
         case 16:
            if (y == 16) {
               var77 = f.aJ[f.c(E[D - 2], E[D - 1]) + 5] & 255;
               if (aC[1] < 0) {
                  --B;
                  if (B < 0) {
                     B = (byte)(B + var77);
                  }
               } else if (aC[2] < 0) {
                  ++B;
                  B = (byte)(B % var77);
               } else if (aC[13] >= 0 && aC[6] >= 0) {
                  if (aP) {
                     E[D] = B;
                     f.a(aB, false);
                  }
               } else {
                  GloftMMN.c(3);
                  --D;
                  f.b(aB, 4);
               }
            } else if (K >= 3) {
               if (aC[3] < 0 && C >= 2) {
                  if (B != 1) {
                     aa = true;
                     B = 1;
                  }
               } else if (aC[4] < 0 && C >= 3) {
                  if (B != 2) {
                     B = 2;
                     aa = true;
                  }
               } else if (aC[1] < 0) {
                  if (B != 0) {
                     B = 0;
                     aa = true;
                  }
               } else if (aC[2] < 0 && C >= 4) {
                  if (B != 3) {
                     B = 3;
                     aa = true;
                  }
               } else if (aP) {
                  i = y;
                  z = 34;
               } else if (aC[13] < 0 || aC[6] < 0) {
                  GloftMMN.c(3);
                  if (y == 3) {
                     y = 0;
                     k = true;
                  } else if (D <= 0) {
                     z = 15;
                  } else {
                     --D;
                     f.b(aB, 4);
                  }
               }
            }

            if (K == 0) {
               f.a(aB, false, true);
            }

            g.p = 0L;
            break;
         case 5:
            if (K == 0) {
               f.a(aB, false, true);
            }

            f.m();
            break;
         case 10:
            if (f.a(false, false)) {
               f.b(aB, 4);
            }
            break;
         case 11:
            var77 = g.h(f.aM, f.aN);
            var87 = g.as[var77 + 8];
            if (f.aq && f.ar) {
               f.aP = g.as[var77 + 6] & 255;
               f.aO = g.as[var77 + 7] & 255;
               var62.e = -1;
               var62.a(g.as[var77 + 5] & 255);
               f.aq = false;
               f.ar = false;
               f.d();
               if (var87 > 0) {
                  g.u = -1;
                  g.a(true, 76);
                  GloftMMN.c(0);
               }

               z = 12;
               break;
            }

            f.d();
            break;
         case 12:
            if (f.aq && f.ar) {
               g.a(false, 76);
               z = 14;
               if (g.ar > 0) {
                  var74 = null;
                  short var86;
                  if ((var86 = g.aq[0]) == g.J) {
                     ++g.K;
                  } else {
                     g.J = var86;
                     g.K = 0;
                  }

                  g.h(f.aM, f.aN);
                  if (g.L != f.av) {
                     g.K = 0;
                  }

                  g.M = null;
                  g.Q = 0;
                  g.J = var86;
                  g.M = g.b(GloftMMN.k(var86), 207);
                  z = 13;
               }
               break;
            }

            f.d();
            break;
         case 13:
            if (aP) {
               z = 14;
               if (g.M != null) {
                  var5 = g.M.length() << 1;
                  if (g.Q < var5) {
                     g.Q = var5;
                     z = 13;
                  }
               }

               if (z == 14) {
                  g.Q = 0;
                  g.M = null;
               }
            }
            break;
         case 14:
            g.x = null;
            var57 = s[f.av];
            var77 = g.h(f.aM, f.aN);
            f.a(var72, g.as, var77 + 10, false, false);
            w = g.as[var77 + 8];
            var57.c(39, w);
            if (w != 0) {
               b(1000);
            }

            B = (byte)(C - 1);
            g.Q = 0;
            if (w < 0 && (E[0] == 2 && E[1] == 2 && x >= 2 || E[0] == 3 && E[1] == 0)) {
               var12 = GloftMMN.c(0, 100);
               if ((var73 = s[0].a[24] - 20) < 10) {
                  var73 = 10;
               }

               if (f.bc[31] == 0 && var73 > 0 && var12 <= var73) {
                  g.f(13, 1);
               }
            }

            z = 7;
            break;
         case 15:
            var57 = s[f.av];
            g.L = f.av;
            var62.a(0);
            f.i();
            g.x = null;
            v[18].e = -1;
            var57.A = false;
            if (D <= 0) {
               D = 0;
               z = 0;
               k = true;
               var57.n();
            } else {
               g.a(var57);
               D = 0;
               if (O) {
                  var57.n();
                  h = 16;
                  System.out.println("!!!!!!!!!!!!!!opennnnnnnnn");
                  k = true;
                  y = 27;
                  O = false;
                  g.au = false;
               } else if (g.N.size() > 0) {
                  z = 17;
               } else {
                  if (g.au && ak == 0) {
                     var57.n();
                     z = 0;
                     k = true;
                     f.av = -1;
                     g.au = false;
                     break;
                  }

                  f.b(aB, 4);
               }
            }
            break;
         case 17:
            var77 = g.N.size();
            if (K < 4 && var77 > 0) {
               if (aP) {
                  i = 255;
               }
            } else {
               if (var77 > 0 && !aP && i != 255) {
                  break;
               }

               i = 0;
               g.Q = 0;
               g.M = null;
               if (var77 > 0) {
                  g.N.removeElementAt(0);
               }

               if (P) {
                  P = false;
                  h = 16;
                  y = 27;
                  g.au = true;
                  z = -1;
                  K = 0;
               } else if (X == 1) {
                  z = 5;
               } else {
                  z = 0;
                  g.c(aB);
                  if (z == 0) {
                     f.a(aB, false, true);
                  }
               }
            }
            break;
         case 22:
            a(true);
            if (z >= 0) {
               f.a(aB, false, true);
            }
            break;
         case 32:
            if (i < 0) {
               GloftMMN.b();
               a(5, 0, true);
               if (i == -2) {
                  U[0] = 0;
               }

               U[2] = 790;
               U[3] = 791;
               U[5] = 0;
               R[0] = 400;
               R[1] = 792;
               R[2] = 890;
               R[3] = 794;
               R[4] = 795;
               i = 0;
               f.a(aB, false, true);
            }

            a(false);
            if (k) {
               i = -2;
            } else if (aP) {
               if ((var77 = U[0]) == 2) {
                  z = 73;
                  d(890, 1);
                  z = 73;
               } else if (var77 == 1) {
                  z = 81;
                  j = true;
                  k = true;
                  f.a(aB, true, false);
               } else if (var77 == 0) {
                  if (f.bc[31] == 0) {
                     i = -1;
                     a(15);
                  } else {
                     g.a((int)903);
                     z = 0;
                  }
               } else if (var77 == 3) {
                  Y = true;
                  z = 74;
                  j = false;
                  f.a(aB, true, true);
               } else if (var77 == 4) {
                  Y = false;
                  z = 74;
                  j = false;
                  f.a(aB, true, true);
               } else {
                  k = true;
                  f.bn = true;
                  y = 0;
                  g.H = -1;
                  f.a(aB, true, true);
               }
            }
            break;
         case 35:
            if (i >= 2) {
               if (K == 0) {
                  aB.setClip(0, 0, 240, 400);
                  aB.setColor(0);
                  aB.fillRect(0, 199, 240, a.z + 5);
                  M.a(aB, GloftMMN.k(834), 120, 200, 17, 0);
               } else if (K == 1) {
                  a(true, false, false);
               } else {
                  z = 0;
                  k = true;
               }
            }
            break;
         case 67:
            k = true;
            aB.setClip(0, 0, 240, 400);
            aB.fillRect(0, 0, 240, 400);
            if (k) {
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 76);
            }

            if (f.m == null) {
               f.m = Image.createImage(f.n, f.o);
               f.aj = f.m.getGraphics();
            }

            Graphics var84;
            (var84 = f.aj).translate(0, -75);
            var8 = v[7];
            var71 = aP || aC[14] < 0;
            d var96 = v[17];
            if (i != -2) {
               if (K == 0) {
                  var84.setColor(0);
                  var84.fillRect(0, 0, 240, 400);
                  f.a(var84, false, true);
                  var8.a(48);
                  var8.a = 30720;
                  var8.b = 51200;
                  var8.a(var84);
                  g.a(GloftMMN.k(828), (int)2);
                  g.ae &= -257;
                  i = -1;
               } else if (i == -1) {
                  var8.a(51);
                  var8.a(var84);
                  if (var71) {
                     i = 0;
                     var84.setColor(0);
                     var84.fillRect(0, 75, 240, 240);
                     var8.a(48);
                     var8.a(var84);
                  }
               } else {
                  var84.setColor(0);
                  var84.fillRect(0, 140, 240, 272);
                  if (aC[1] < 0) {
                     --i;
                  } else if (aC[2] < 0) {
                     ++i;
                  }

                  if (i < 0) {
                     i = 5;
                  }

                  i %= 6;
                  var8.a(49);
                  int[] var100 = var8.c(1 + i);
                  var84.setColor(16545380);
                  u[0].a((Graphics)var84, 30, 4, 220, 380, 0, 0, 0);
                  var84.fillRect(0, var100[1] + 6 - 3 - 12, 240, 35);

                  for(var5 = 0; var5 < 2; ++var5) {
                     u[1].a((Graphics)var84, 92 + var5, 0, var5 * 240, var100[1] - 1, 0, 0, 0);
                  }

                  var8.a(var84);
                  if (var71) {
                     GloftMMN.c(3);
                     f.bc[29] = (byte)i;
                     var96.a(89);
                     var96.a = 30720;
                     var96.b = var100[1] + 14 << 8;
                     i = -2;
                  }
               }
            }

            M.b(var84, GloftMMN.k(820), 120, 90, 17, 1);
            aB.drawImage(f.m, 0, 75, 20);
            if (i == -2) {
               var96.a(aB);
               if (var96.a()) {
                  aB.setColor(0);
                  aB.fillRect(0, 0, 240, 400);
                  X = 1;
                  s[0].a[22] = 41;
                  f.bc[30] = 1;
                  H = 36;
                  g.a(GloftMMN.k(753), (int)2);
                  g.ae &= -257;
                  L = 0;
                  y = 20;
                  z = -1;
                  f.d = true;
               } else {
                  var96.b();
               }
            }

            var84.translate(0, 75);
         }

         if (h != 8) {
            return;
         }

         if (y != 20 && (y < 11 || y > 16) && y != 7) {
            a(var72);
         }

         var11 = K == 0;
         var71 = false;
         if (!(var78 = y == 35 && !k || y == 20 || y == 8 || y == 75 || y == 67 || y == 68) && y != 15 && y != 33 && y != 3 && y != 4 && y != 16 && (var11 || y != 7 && y != 13 && y != 17 && y != 34 && y != 32 && y != 73 && y != 74 && y != 81)) {
            var71 = true;
         }

         if (y == 17 && z == 17) {
            var71 = true;
         }

         if (var71 || k && !var78) {
            if (k) {
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 400);
            }

            f.n();
            f.a(aB);
            f.o();
         }

         d var99;
         String var111;
         switch(y) {
         case 0:
            if (f.aR >= 0) {
               f.a(aB, (d)null, false, true, false, (g)null);
            }
         case 1:
         case 2:
         case 5:
         case 9:
         case 10:
         case 11:
         case 12:
         case 13:
         case 14:
         case 15:
         case 17:
         case 18:
         case 19:
         case 20:
         case 22:
         case 24:
         case 25:
         case 26:
         case 27:
         case 28:
         case 29:
         case 30:
         case 31:
         case 32:
         case 35:
         case 36:
         case 37:
         case 38:
         case 39:
         case 40:
         case 41:
         case 42:
         case 43:
         case 44:
         case 45:
         case 46:
         case 47:
         case 48:
         case 49:
         case 50:
         case 51:
         case 52:
         case 53:
         case 54:
         case 55:
         case 56:
         case 57:
         case 58:
         case 59:
         case 60:
         case 61:
         case 62:
         case 63:
         case 64:
         case 65:
         case 66:
         case 67:
         case 69:
         case 70:
         case 71:
         case 72:
         case 77:
         case 78:
         case 79:
         case 80:
         default:
            break;
         case 3:
         case 4:
            if (z < 0) {
               f.c(aB, y);
            }
            break;
         case 6:
            if (K > 10) {
               g.H = -1;
               z = 7;
            }
            break;
         case 7:
            f.c(aB);
            break;
         case 8:
            var7 = n[0];
            byte var105 = var72.b[17];
            var15 = 16751593;
            if (s[0].a[0] == 0) {
               var15 = 706047;
            }

            if (K != 0 && !k) {
               aB.setColor(4472414);
               aB.fillRect(4, 98, 226, 99);
               var7.a = 29952;
               var7.b = 46848;
               aB.setColor(6117777);
               aB.fillRect(72, 99, 93, 83);
               v[17].d.a((Graphics)aB, 86, 0, var7.a >> 8, var7.b >> 8, 0, 0, 0);
               aB.setColor(var15);
               aB.fillRect(5, 83, 41, 14);
               aB.fillRect(188, 83, 39, 14);
               aB.fillRect(5, 291, 226, 15);
               var7.a(aB);
               var7.b();
               d var113;
               (var113 = v[19]).i = var7;
               var113.a = 0;
               var113.b = -16128;
               var113.a(aB);
               var113.b();
               (var99 = v[17]).a(69);
               var99.a = 768;
               var99.b = 20480;
               var99.a(aB);
               var99.a += 47360;
               var99.a(aB);
               var99.a = 25088;
               var99.b = 73984;
               var99.a(aB);
               var99.b();
               M.a(aB, GloftMMN.k(938), 117, 315, 17, 0);
               if (var99.a()) {
                  var99.e = -1;
               }

               if (var105 > 0) {
                  M.a(aB, GloftMMN.k(g.d(var105, 0)), 120, 197, 17, 0);
                  f.a((Graphics)aB, var105, 79, 211, 5);
               }

               var90 = false;

               for(var5 = 0; var5 < aC.length; ++var5) {
                  if (aC[var5] < 0) {
                     var90 = true;
                     break;
                  }
               }

               if (var90 && K >= 5) {
                  bB = -1;
                  bC = -1;
                  if (g.V != 0) {
                     if (g.V < 0) {
                        s[0].a[6] = -g.V - 1;
                        s[0].a[7] = g.W & 255;
                     } else {
                        s[0].a[8] = g.V - 1;
                        s[0].a[9] = g.W & 255;
                     }

                     g.V = 0;
                     z = 68;
                     i = 0;
                  } else {
                     z = 75;
                  }
               }
               break;
            }

            z = -1;
            var72.b[16] = (byte)var7.e;
            var7.a(43);
            var7.d.m = var72.b[15];
            v[19].e = -1;
            v[19].a(75);
            aB.setColor(0);
            aB.fillRect(0, 0, 240, 400);
            aB.setClip(0, 75, 240, 260);
            aB.setColor(4472414);
            aB.fillRect(0, 75, 240, 240);
            g.a(aB, 3, 81, 229, 227, var15, 16777099, 16777099);
            M.a(aB, GloftMMN.k(657), 117, 83, 17, 0);
            aB.setColor(4472414);
            aB.fillRect(4, 98, 226, 127);
            var111 = g.b((var111 = (String)g.N.elementAt(0)).substring(1, var111.length()), 196);
            M.a(aB, var111, 120, 229, 17, 0);
            aB.setColor(16777099);
            aB.fillRect(4, 97, 226, 1);
            if (!k) {
               GloftMMN.c(1);
            }

            b(1000);
            break;
         case 16:
            if (z < 0) {
               f.b(aB, false);
            }
            break;
         case 21:
            var14 = U[0];
            var15 = R[var14] - 141;
            if (var14 == U[1] - 1) {
               z = 0;
            } else if (var14 == U[1] - 2) {
               i = -1;
               b();
               a(15);
            } else if (g.ah[var15] == 0) {
               if ((var111 = GloftMMN.k(435 + var15)) != null) {
                  g.a(var111);
               } else {
                  g.a((int)867);
               }

               z = 0;
            } else {
               H = var15;
               z = 18;
               L = 0;
            }
            break;
         case 23:
            if (U[0] == 0) {
               H = R[2];
               z = 18;
               L = 0;
            } else {
               z = 0;
            }
            break;
         case 33:
            if (K == 0) {
               f.a(aB, false, true);
            }

            a(aB);
            if (aP) {
               if (f.av != -1) {
                  s[f.av].A = false;
               }

               z = 0;
               f.a(aB, false, true);
            }
            break;
         case 34:
            aB.setClip(0, 75, 240, 240);
            f.c(aB, i);
            if (v[4].a()) {
               if (i == 3) {
                  f.a(true, false);
               } else if (D < 3) {
                  E[D++] = B;
                  f.b(aB, 4);
               }
            }
            break;
         case 68:
            GloftMMN.b();
            if (i == 0) {
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 400);
               M.a(aB, GloftMMN.k(829), 120, 200, 3, 0);
            } else if (i == 1) {
               f.m = null;
               f.aj = null;
               GloftMMN.n = null;
               GloftMMN.o = null;
               f.b(false);
            } else if (i == 2) {
               g.a(false);
            } else if (i == 3) {
               var5 = s[0].a[0];
               g.j[var5] = 0;
               s[0].a(7 + var5 * 2, 0, true);
            } else if (i == 4) {
               g.a(true);
            } else {
               au = true;
               f.m = Image.createImage(f.n, f.o);
               f.aj = f.m.getGraphics();
               GloftMMN.a("/3", 0);
               f.b(true);
               if (g.V == 0) {
                  z = 75;
               } else {
                  z = 8;
               }
            }

            ++i;
            break;
         case 73:
            if (aP) {
               f.a(aB);
            }

            if (d(890, 2)) {
               z = 32;
               i = -1;
            }
            break;
         case 74:
            if (g.ak == null) {
               g.a(GloftMMN.k(866), (byte)-56);
               i = 0;
            }

            a(aB);
            if (aP) {
               g.ak = null;
               if (i == 0) {
                  z = 32;
                  bP = 0;
                  Y = false;
               } else if (i == 1) {
                  z = 81;
               }
            }
            break;
         case 75:
            n[0].a(var72.b[16]);
            if (g.N.size() > 0) {
               g.N.removeElementAt(0);
            }

            var72.b[17] = 0;
            z = 0;
            g.c(aB);
            if (z != 4) {
               if (z == 17) {
                  z = 0;
               } else if (z != 18) {
                  z = 0;
               }
            }

            if (z == 18) {
               L = 0;
               y = 20;
               z = -1;
            } else {
               f.d = true;
               f.E = -10000;
               f.F = -10000;
               g.H = -1;
            }

            g.p = 0L;
            break;
         case 76:
            if (K == 0) {
               g.a(GloftMMN.k(906 + ai - 1), (byte)-56);
               i = 0;
            } else if (K > 3) {
               a(aB);
               if (aP) {
                  if (i == 0) {
                     z = 0;
                  } else if (i == 1) {
                     i = 2;
                     z = 35;
                  }
               }
            }
            break;
         case 81:
            if (g.ak == null) {
               if (!j) {
                  g.a(GloftMMN.k(921), (byte)-56);
               } else {
                  g.a(GloftMMN.k(866), (byte)-56);
               }

               i = 0;
            }

            a(aB);
            if (aP) {
               g.ak = null;
               if (i == 1) {
                  if (j) {
                     j = false;
                     i = 2;
                     K = 0;
                     z = 35;
                  } else {
                     z = 83;
                  }
               } else if (j) {
                  j = false;
                  i = 0;
                  z = 0;
                  k = true;
               } else {
                  z = 82;
               }
            }
            break;
         case 82:
            if (!Y) {
               h = -1;
               return;
            }

            Z = true;
            a(4);
            f.bd = 0;
            f.a(aB, H);
            f.e();
            f.b();
            i = 0;
            y = -1;
            break;
         case 83:
            aB.setClip(0, 0, 240, 400);
            aB.setColor(0);
            aB.fillRect(0, 199, 240, a.z + 5);
            M.a(aB, GloftMMN.k(834), 120, 200, 17, 0);
            if (K == 1) {
               a(true, false, false);
            }

            if (K > 1) {
               z = 82;
            }
         }

         if (y == -1) {
            return;
         }

         if (f.d && y != 20) {
            aB.setClip(0, 75, 240, 240);
            aB.setColor(0);
            aB.fillRect(0, 75, 240, 240);
            if (z == -1) {
               f.d = false;
            }
         } else if (y == 18) {
            if (K == 0) {
               aB.setClip(0, 0, 240, 400);
               v[5].d.a((Graphics)aB, 3, 0, v[5].a >> 8, v[5].b >> 8, 0, 0, 0);
            }

            if (!a(L, 0) && !k) {
               L += (int)aH;
            } else {
               L = 0;
               z = 20;
            }
         } else if (y == 20) {
            if (L == 0) {
               f.bd = 0;
               L = 1;
            } else if (L == 1) {
               f.a(aB, H);
            }
         } else if (y == 19) {
            if (z == -1) {
               if (N[0] >= 0) {
                  aB.setClip(0, 0, 240, 400);
                  aB.setColor(0);
                  aB.fillRect(0, 0, 240, 400);
               } else if (!a(L, 1) && !k) {
                  L += (int)aH;
               } else {
                  z = 0;
                  f.a(aB, true, true);
                  System.out.println("TTTTTTTTTTTTTTTTTTT");
                  v[6].a(aB);
               }
            }
         } else if (y != 67) {
            if (y == 17) {
               if (K == 0) {
                  f.a(aB, false, true);
               }

               g.b(aB);
            }

            boolean var107 = y == 8 || y == 68;
            if (X != 1 && !var107) {
               var15 = K % 10;
               if (k) {
                  f.a(aB, true, true);
               }

               var16 = (g.ae & 256) == 256;
               aB.setClip(0, 0, 240, 400);
               var99 = v[5];
               var18 = v[6];
               if (var16 && y == 0 && K != 0 && !k && z <= 0) {
                  var99.d.a((Graphics)aB, 3, 0, v[5].a >> 8, v[5].b >> 8, 0, 0, 0);
                  var99.a(1);
                  var99.a(aB);
                  var18.a(5);
                  var18.g = var99.g;
                  var18.f = var99.f;
                  var18.a(aB);
                  var99.b();
               } else {
                  var18.a(4);
                  if (y != 3 && y != 4 && y != 16 && y != 34 && y != 32 && y != 73 && y != 35 && y != 81 && y != 74 && y != 75) {
                     if (var99.e == 1 || K == 0) {
                        var99.d.a((Graphics)aB, 3, 0, v[5].a >> 8, v[5].b >> 8, 0, 0, 0);
                        var99.a(0);
                        var99.a(aB);
                     }

                     aB.setClip(0, 0, 240, 400);
                     var18.a(aB);
                  }
               }

               if ((var15 & 1) == 0 || k) {
                  f.b(aB);
               }

               f.i();
               if (y != 1 || N[0] < 0) {
                  if (var15 != 0 && y != 5 && !k) {
                     f.c(aB, true);
                  } else if (z < 0) {
                     if (y != 5 && !k) {
                        f.c(aB, K % 40 != 0);
                     } else {
                        f.c(aB, false);
                     }
                  }

                  if (z < 0) {
                     f.d(aB, y == 6);
                  }

                  if (y == 0) {
                     g.a(aB);
                  }
               }

               if (y == 13) {
                  if (g.ar > 0) {
                     g.a(aB, false, 103, false);
                     g.a(aB, true, 103, false);
                  } else {
                     g.a(aB, false, 195, false);
                  }

                  g.R = 0L;
               } else if (y == 11 || y == 12 || y == 14 || y == 7 && f.av != -1 || g.p != 0L && f.av != -1 && y != 16 && y != 17) {
                  if (y == 0 && v[2].e != 3) {
                     g.R = 0L;
                  } else {
                     g.a(aB, false, 195, y == 0);
                  }
               }

               if (f.au != -1 && y == 3) {
                  aB.setClip(0, 75, 240, 240);
                  var21 = f.i(var20 = f.aX[B], 5);
                  if ((var22 = f.g(f.i(var20, 5), 2)) > 0) {
                     g.a(aB, 0, 78, 120, 21, 13092848, 16777215, 0);
                     M.a(aB, f.a((long)var22 * 60L, false), 3, 80, 20, 1);
                  }

                  var19 = f.ao[f.g(var21, -1)];
                  if ((var19 = f.a(s[0], f.an, var19 + 6, false, true)) > 0) {
                     g.a(aB, 120, 78, 120, 21, 16768667, 16777215, 0);
                     M.a(aB, "-" + f.e(var19) + "$", 237, 80, 24, 1);
                  }
               }
            }
         }

         if (h == 8) {
            ++K;
            if (z >= 0) {
               y = z;
               z = -1;
               K = 0;
            }
         }

         aB.setClip(0, 0, 240, 400);
         if (ak != 0) {
            aB.setColor(0);
            aB.fillRect(43, 16, 132, 50);
            aB.setColor(16777215);
            aB.drawString(aq[ak], 43, 16, 20);
         }

         switch(ak) {
         case 0:
         case 1:
         default:
            break;
         case 2:
            var14 = 16 + aB.getFont().getHeight();
            aB.setColor(65280);
            aB.drawString(GloftMMN.k(180 + an[al]) + "=" + s[0].a[an[al]], 43, var14, 20);
            break;
         case 3:
            var14 = 16 + aB.getFont().getHeight();
            if (f.av == -1) {
               aB.setColor(16711680);
               aB.drawString("no one there !", 43, var14, 20);
            } else {
               g var109;
               if ((var109 = s[f.av]).a[42] > 0) {
                  aB.setColor(16711680);
                  aB.drawString("love=" + var109.a[39] + "/100", 43, var14, 20);
               } else {
                  aB.setColor(16776960);
                  aB.drawString("friendship=" + var109.a[39] + "/100", 43, var14, 20);
               }
            }
         }

         if ((y == 22 || y == 32) && i >= 0) {
            if (K == 0) {
               f.a(aB, false, true);
            }

            a(aB, false, false);
         }
         break;
      case 15:
         b();
         aB.setColor(0);
         aB.fillRect(0, 0, 240, 400);
         k = true;
         var6 = v[7];
         var7 = v[2];
         if (k) {
            f.a(aB, true, true);
            var60 = var6.e;
            f.c(aB, false);
            var6.a(var60);
            var6.a(aB);
         }

         if (i == -1) {
            aB.setClip(0, 0, 240, 400);
            aB.setColor(0);
            aB.fillRect(0, 0, 240, 400);
            i = -2;
            f.a(aB, false, true);
         }

         if (i == -2) {
            f.b();
            f.m = null;
            f.aj = null;
            byte var63 = 1;
            if (f.l()) {
               var63 = 0;
            } else if (f.j()) {
               var63 = 3;
            } else if (f.k()) {
               var63 = 2;
            }

            GloftMMN.b("/1");
            a.v = new int[16384];
            a(0, u, 5, false, true, 4, true);
            u[5].a(var63, 0, -1, -1, 0);
            a.v = null;
            GloftMMN.f();
            i = g.h[H];
            y = 30;
            k = true;
            f.a(aB, true, true);
            f.c(aB, false);
            var6.a(32);
            var6.a = 30720;
            var6.b = 51200;
            var7.e = -1;
         } else if (i <= -3) {
            a(aB, false, false);
            g.b(aB);
            if (aP) {
               GloftMMN.c(3);
               y = 30;
               i = -i - 3;
            }
         } else {
            U[2] = 783 + i;
            if (aC[13] < 0) {
               GloftMMN.c(3);
               a(8);
               y = 20;
               Y = true;
               L = 0;
            }

            if (y == 31) {
               a(false);
               a(aB, false, false);
               if (aP || aC[14] < 0) {
                  if (U[0] == U[1] - 1) {
                     y = 30;
                     f.a(aB, true, true);
                     k = true;
                  } else {
                     var60 = R[U[0]] - 141;
                     if (g.ah[var60] == 0) {
                        i *= -1;
                        i -= 3;
                        g.a((int)867);
                        k = true;
                     } else {
                        int var64 = g.i[g.h[H] * 7 + i] & 255;
                        f.aT += (long)var64 * 60L * 1000L / 60L;
                        f.c((Graphics)null, true);
                        H = var60;
                        a(8);
                        y = 20;
                        L = 0;
                     }
                  }
               }
            } else if (y == 30) {
               var6.a(aB);
               boolean var69 = aC[3] < 0;
               boolean var65 = aC[4] < 0;
               var10 = aC[1] < 0;
               var11 = aC[2] < 0;
               byte[] var82 = new byte[]{6, 1, 3, 3, 0, 2, 0, 4, 1, 5, 1, 5, 0, 4, 0, 0, 3, 5, 1, 0, 4, 6, 2, 0, 5, 0, 2, 0};
               var14 = var73 = i;
               if (var69) {
                  var14 = var82[i * 4 + 0];
               } else if (var65) {
                  var14 = var82[i * 4 + 1];
               } else if (var10) {
                  var14 = var82[i * 4 + 2];
               } else if (var11) {
                  var14 = var82[i * 4 + 3];
               }

               if (var14 != var73) {
                  var7.e = -1;
                  i = var14;
               }

               var15 = 0;

               byte var88;
               for(var80 = 0; var80 < 2; ++var80) {
                  for(var5 = 0; var5 < g.h.length; ++var5) {
                     var88 = g.g[var5];
                     if (g.h[var88] == i && g.f[var88] == 1) {
                        if (var80 == 1) {
                           R[var15] = 141 + var88;
                           if (f.bc[25] != -1) {
                              byte var92 = f.bc[24];
                              if (var88 == var92) {
                                 S[var15] = 7;
                              }
                           }

                           if (g.ah[var88] == 0) {
                              S[var15] = 25;
                           }
                        }

                        ++var15;
                     }
                  }

                  if (var80 == 0) {
                     a(var15, 0, true);
                     R[var15] = 400;
                     var15 = 0;
                  }
               }

               U[3] = 401;
               U[5] = -1;
               if ((aP || aC[14] < 0) && !var69 && !var10 && !var65 && !var11) {
                  y = 31;
               }

               a(aB, true, false);
               var88 = g.h[H];
               var95 = i;
               if ((var19 = g.i[var88 * 7 + var95] & 255) > 0) {
                  g.a(aB, 120, 288, 120, 21, 13092848, 16777215, 0);
                  M.a(aB, f.a((long)var19 * 60L, false), 123, 290, 20, 1);
               }
            }
         }

         aB.setClip(0, 0, 240, 400);
         if (var7.e == -1) {
            var7.a(2);
         }

         if (h != 15) {
            u[5] = null;
            Q = null;
            System.gc();
         }

         ++K;
         return;
      case 16:
         long var59 = 0L;
         var68 = v[7];
         int[] var76;
         g var81;
         switch(y) {
         case -1:
            GloftMMN.c(3);
            a(8);
            y = 0;
            f.a(aB, true, true);
            g.p = 1000L;
            f.be = null;
            K = 0;
            break;
         case 24:
            b();
            if (K == 0 || k) {
               var68.a = 30720;
               var68.b = 50176;
               var68.a(13);
               var68.a(aB);
            }

            if (aC[13] < 0 || aP) {
               y = -1;
               if ((g.ae & 512) != 0) {
                  bF[2] = 0;
                  bF[3] = 0;
                  bF[4] = 0;
                  bF[0] = 0;
                  bF[1] = 0;
                  y = 25;
                  K = -1;
                  g.ae |= -513;
               }
            }
            break;
         case 25:
            if (K == 0 || k) {
               var68.a = 30720;
               var68.b = 51200;
               var68.a(15);
               var68.a(aB);
            }

            if (bF[1] == 1) {
               a(false);
               a(aB, false, false);
               if (aP) {
                  g.f(11, 1 + U[0]);
                  y = -1;
               }
            } else {
               (var68 = v[9]).a(17);
               var68.a = 30720;
               var68.b = 51200;
               var68.a(aB);
               var68.b();
               b();
               a(var68);
               b(0, 200, 240, 400, 3, 0);
               bF[2 + bF[0]] = 1;
               if (aP) {
                  if (bF[2] + bF[3] + bF[4] == 3) {
                     a(3, 0, false);
                     R[0] = s[1].a[1];
                     R[1] = s[2].a[1];
                     R[2] = s[3].a[1];
                     U[2] = 812;
                     U[3] = 813;
                     U[5] = 25;
                     bF[1] = 1;
                  } else {
                     aC[4] = -1;
                  }
               }

               if (aC[3] < 0) {
                  K = -1;
                  var10002 = bF[0]--;
                  if (bF[0] < 0) {
                     bF[0] = 2;
                  }
               }

               if (aC[4] < 0) {
                  K = -1;
                  var10002 = bF[0]++;
                  var10000 = bF;
                  var10000[0] %= 3;
               }
            }
            break;
         case 26:
            b();
            aB.setClip(0, 0, 240, 400);
            var11 = k;
            if (K == 0) {
               var11 = true;
               bF[0] = 0;
            }

            if (V == null) {
               var12 = 0;

               for(var73 = 0; var73 < 2; ++var73) {
                  for(var5 = 0; var5 < s[0].a.length; ++var5) {
                     if ((g.d(var5, 1) & 1) != 1) {
                        if (var73 == 1) {
                           V[var12] = var5;
                        }

                        ++var12;
                     }
                  }

                  if (var73 == 0) {
                     V = new int[var12];
                     var12 = 0;
                  }
               }
            }

            if (aC[1] < 0) {
               var11 = true;
               var10000 = bF;
               var10000[0] -= 2;
               if (bF[0] < 0) {
                  bF[0] = 0;
               }
            }

            if (aC[2] < 0) {
               var11 = true;
               var10000 = bF;
               var10000[0] += 2;
               if (bF[0] >= V.length - 1 - 3) {
                  bF[0] = V.length - 1 - 3;
               }
            }

            if (var11) {
               var68.a = 30720;
               var68.b = 50176;
               var68.a(18);
               var68.a(aB);
            }

            (var68 = v[9]).a(19);
            var68.a = 30720;
            var68.b = 51200;
            b(((var76 = var68.c())[0] >> 8) - 20, var76[1] >> 8, (var76[2] - var76[0] >> 8) + 40, var76[3] - var76[1] >> 1 >> 8, 4, 0);
            b((var76[0] >> 8) - 20, var76[1] + var76[3] >> 1 >> 8, (var76[2] - var76[0] >> 1 >> 8) + 40, var76[3] - var76[1] >> 1 >> 8, 5, 0);
            var68.a(aB);
            var68.b();
            if (aC[13] < 0 || aC[6] < 0) {
               y = -1;
            }
            break;
         case 27:
            b();
            var81 = null;
            var14 = bF[1];
            var15 = -1;
            if (var14 >= 0) {
               var15 = g.G[var14];
            }

            if (var15 >= 0) {
               var81 = s[var15];
            }

            if (K >= 2 && !k) {
               if (U[1] > 0) {
                  a(aB, false, false);
                  a(false);
                  if (aC[14] < 0 || aP) {
                     if (U[0] == U[1] - 1) {
                        K = -1;
                     } else {
                        bF[3] = R[U[0]] - 141;
                        U[1] = -1;
                        if (f.bc[25] > 0) {
                           g.a(GloftMMN.k(882), (byte)40);
                        } else {
                           g.a(GloftMMN.k(878) + GloftMMN.k(var81.a[1]) + GloftMMN.k(877) + GloftMMN.k(141 + bF[3]) + GloftMMN.k(879), (byte)40);
                        }

                        i = 0;
                     }
                  }
                  break;
               }

               if (U[1] < 0) {
                  a(aB);
                  if (aP) {
                     if (i == 0) {
                        K = -1;
                     } else if (i == 1) {
                        f.bc[25] = (byte)var15;
                        f.bc[24] = (byte)bF[3];
                        g.a(GloftMMN.k(var81.a[1]) + GloftMMN.k(876) + GloftMMN.k(141 + bF[3]));
                        g.au = true;
                        y = -1;
                     }
                  }
                  break;
               }

               var68.a = 30720;
               var68.b = 50176;
               var68.a(20);
               int[] var104 = var68.c(11);
               var68.a = var104[0] << 8;
               var17 = 0;

               for(var5 = 0; var5 < g.G.length && g.G[var5] >= 1; ++var5) {
                  aB.setClip(0, 0, 240, 400);
                  if (f.d((var57 = s[g.G[var5]]).b[13]) == -1) {
                     if (bF[2] == var17) {
                        var68.a(21);
                        bF[1] = var5;
                     } else {
                        var68.a(22 + var17 % 2);
                     }

                     int[] var94 = var68.c();
                     GloftMMN.k(var57.a[1]);
                     bF[0] = var5;
                     var68.b = (var104[1] << 8) + var17 * (var94[3] - var94[1]);
                     b((var76 = var68.c())[0] >> 8, var76[1] >> 8, var76[2] - var76[0] >> 8, var76[3] - var76[1] >> 8, 22, var17);
                     if (bF[0] == bF[1] || bF[0] == bF[4] || K <= 3 || bF[4] == -1) {
                        var68.a(aB);
                     }

                     ++var17;
                  }
               }

               if (bF[4] == -1) {
                  bF[4] = 0;
               }

               if (var5 == 0) {
                  as = true;
                  M.a(aB, GloftMMN.k(805), 120, 200, 3, 0);
               } else {
                  as = false;
               }

               if (var17 > 0) {
                  if (aC[1] < 0) {
                     bF[4] = bF[1];
                     var10002 = bF[2]--;
                  }

                  if (aC[2] < 0) {
                     bF[4] = bF[1];
                     var10002 = bF[2]++;
                  }

                  if (bF[2] < 0) {
                     bF[2] = var17 - 1;
                  }

                  var10000 = bF;
                  var10000[2] %= var17;
                  if (f.bc[31] == 0 && (g.ah[14] == 1 || g.ah[7] == 1 || g.ah[28] == 1) && (aC[14] < 0 || aP)) {
                     a(2, 0, true);
                     U[2] = 802;
                     U[3] = 791;
                     U[5] = -1;
                     R[0] = 908;
                     R[1] = 909;
                     z = 77;
                  }
               }

               if (aC[13] < 0 || aC[6] < 0) {
                  y = -1;
               }
               break;
            }

            System.out.println("------------- CONTACT LIST INIT ------------------");
            System.out.println("s_game_refreshAll=" + k);
            var68.a = 30720;
            var68.b = 50176;
            var68.a(20);
            var68.a(aB);
            if (!k) {
               bF[2] = 0;
               U[1] = 0;
            }

            var80 = 0;

            for(var5 = 0; var5 < g.G.length && g.G[var5] >= 1; ++var5) {
               if (f.d(s[g.G[var5]].b[13]) == -1) {
                  ++var80;
               }
            }

            as = var80 == 0;
            bF[4] = -1;
            break;
         case 77:
            a(aB, false, false);
            a(false);
            if (aC[14] >= 0 && !aP) {
               break;
            }

            if (U[0] == U[1] - 1) {
               U[1] = 0;
               k = true;
               y = 27;
               K = -1;
            } else {
               if (U[0] == 0) {
                  a(3, 0, true);
                  U[2] = 908;
                  U[3] = 781;
                  U[5] = -1;
                  var80 = 0;
                  if (f.bc[31] == 0) {
                     if (g.ah[7] == 1) {
                        ++var80;
                        R[0] = 148;
                     }

                     if (g.ah[28] == 1) {
                        R[var80++] = 169;
                     }

                     if (g.ah[14] == 1 && !g.ai[14]) {
                        R[var80++] = 155;
                     }

                     if (var80 != 0) {
                        R[var80] = 0;
                        S[var80] = 21;
                        ++var80;
                     }
                  }

                  U[1] = var80;
                  k = true;
                  y = 27;
                  K = 0;
                  break;
               }

               var80 = g.G[bF[1]];
               var81 = s[var80];
               if (var80 != f.bc[25] && var80 != f.bc[26]) {
                  i = 0;
                  g.a(GloftMMN.k(910) + "{" + GloftMMN.k(var81.a[1]) + "}", (byte)40);
                  y = 78;
               } else {
                  g.a(GloftMMN.k(911) + "{" + GloftMMN.k(var81.a[1]) + "} " + GloftMMN.k(912));
                  y = 79;
               }

               K = 0;
            }
            break;
         case 78:
            a(aB);
            if (!aP) {
               break;
            }

            if (i == 1) {
               s[g.G[bF[1]]].e = null;

               for(var5 = bF[1]; var5 < 24; ++var5) {
                  g.G[var5] = g.G[var5 + 1];
               }
            }

            y = 27;
            K = -1;
            U[1] = 0;
            k = true;
            break;
         case 79:
            g.b(aB);
            if (aP) {
               g.N.removeAllElements();
               y = 27;
               K = -1;
               U[1] = 0;
               k = true;
            }
         }

         aB.setClip(0, 0, 240, 400);
         if (h != 8 && (K == 0 || k)) {
            if (y != 27 && y < 77) {
               f.a(aB, true, true);
            } else {
               f.a(aB, true, y < 77 && U[1] == 0);
            }

            var77 = (var68 = v[7]).a;
            var12 = var68.b;
            var73 = var68.e;
            var68.a = 30720;
            var68.b = 51200;
            f.aV = -1;
            f.c(aB, false);
            g.H = -1;
            f.d(aB, false);
            var68.a = var77;
            var68.b = var12;
            var68.e = var73;
         }

         if (h != 8 && y >= 0 && y != 77 && y != 78 && y != 79) {
            if (y != 25 && y > 0) {
               if ((var77 = y) > 25) {
                  --var77;
               }

               var77 = 8 + (var77 - 24) * 2;
               v[8].a(var77);
               v[8].a = 30720;
               v[8].b = 51200;
               v[8].d.a((Graphics)aB, v[8].e - 1, 0, 120, 200, 0, 0, 0);
               a(((var76 = v[8].c())[0] >> 8) - 40, (var76[1] >> 8) - 20, (var76[2] - var76[0] >> 1 >> 8) + 40, (var76[3] - var76[1] >> 8) + 40, 1, 52, 2, 52);
               a(var76[0] + var76[2] >> 1 >> 8, (var76[1] >> 8) - 20, (var76[2] - var76[0] >> 1 >> 8) + 40, (var76[3] - var76[1] >> 8) + 40, 1, 54, 2, 54);
               v[8].a(aB);
               v[8].b();
            }

            if ((g.ae & 512) == 0 && y != 25 && g.ak == null) {
               if (aC[3] == 1) {
                  z = (byte)(y - 1);
                  if (z == 25) {
                     --z;
                  }

                  if (z < 24) {
                     z = 27;
                  }
               }

               if (aC[4] == 1) {
                  z = (byte)(y + 1);
                  if (z == 25) {
                     ++z;
                  }

                  if (z > 27) {
                     z = 24;
                  }
               }
            }

            if (z >= 0) {
               y = z;
               z = -1;
               K = 0;
               return;
            }

            ++K;
            return;
         }
         break;
      case 17:
         var10000 = new int[]{0, 0, 0, 0};
         var75 = null;
         int[] var54 = new int[]{0, 0, 0, 0};
         g var58 = s[0];
         var62 = n[0];
         var68 = v[7];
         var74 = v[8];
         aB.setClip(0, 0, 240, 400);
         if (!at) {
            Q = null;
            g.a(false);

            for(var56 = 4; var56 <= 10; var56 += 2) {
               var58.a[var56] = 0;
               var58.a[var56 + 1] = 0;
            }

            var58.a[6] = 0;
            var58.a[4] = 0;
            var58.a[5] = 9;
            var58.a[9] = 17;
            var58.a[7] = 41;
            var58.a[12] = 1;
            var56 = var58.a[9] / 8;
            var58.a[9] = var56 * 8 + var58.a[12];
            var56 = var58.a[7] / 8;
            var58.a[7] = var56 * 8 + var58.a[12];
            var58.a(7 + var58.a[0] * 2, 0, true);
            af = GloftMMN.k(417);
            i = 0;
            k = true;

            try {
               GloftMMN.b("/18");
               GloftMMN.a(5);
               GloftMMN.f();
               GloftMMN.a(5, 1);
            } catch (Exception var41) {
            }

            at = true;
         }

         if (i == -1 || k) {
            aB.setColor(9806814);
            aB.fillRect(0, 0, 240, 400);
            if (k && af != null && af.indexOf(45) != -1) {
               if (var58.a[0] == 0) {
                  af = GloftMMN.k(402);
               } else {
                  af = GloftMMN.k(417);
               }
            }

            i = 0;
            var68.a = 30720;
            var68.b = 51200;
            var68.a(33);
            var68.a(aB);
            f.a(aB, true, true);
         }

         g.j[0] = 0;
         g.j[1] = 0;
         var71 = false;
         var78 = false;
         if (i != -1) {
            if (g.ak != null) {
               a(aB);
               aB.setColor(0);
               aB.fillRect(0, 317, 240, 25);
               if (aP) {
                  if (i == 0) {
                     g.ak = null;
                     i = -1;
                  } else {
                     GloftMMN.b(5);
                     g.a(true);
                     a(8);
                     y = 67;
                     K = 0;
                     z = -1;
                     i = 0;
                  }
               }
            } else {
               b();
               var15 = ((var14 = 7 + var58.a[0] * 2) - 7) / 2;
               var16 = aC[3] == 1;
               boolean var83 = aC[4] == 1;
               var90 = aC[1] == 1;
               boolean var91 = aC[2] == 1 || aP && i != 2 && i != 1;
               if (i == 1 && aC[5] < 0) {
                  i = 63;
                  bF[2] = 0;
                  bF[3] = -1;
                  bF[4] = -1;
                  bF[5] = -1;
                  af = "----------";
                  r();
               } else if (i != 63) {
                  if (var90) {
                     --i;
                  }

                  if (var91) {
                     ++i;
                  }

                  if (i < 0) {
                     i = 9;
                  }

                  if (i > 9) {
                     i = 0;
                  }
               }

               var93 = false;
               var106 = 0;
               int[] var101 = null;
               var23 = null;
               var68.a = 30720;
               var68.b = 51200;
               var68.e = -1;
               var68.a(33);
               byte var108 = -1;
               int var114 = -1;
               if (i == 3 || i == 6) {
                  var108 = 5;
               }

               if (i == 4 || i == 7) {
                  var108 = 2;
               }

               if (i == 5 || i == 8) {
                  var108 = 4;
               }

               if (var108 != -1) {
                  var114 = 1 + p[var15][var108].a / r[var15][var108];
                  if (var108 == 5) {
                     if (var58.a[0] == 0) {
                        var114 += g.X.length - 1;
                     } else {
                        var114 += g.Y.length - 1;
                     }
                  }

                  bF[0] = var114;
                  bF[1] = var108;
               }

               if (i == 0) {
                  var106 = 35;
                  var101 = var68.c(26);
                  if (var16 || var83) {
                     if (var58.a[0] == 1 && af.compareTo(GloftMMN.k(417)) == 0) {
                        af = GloftMMN.k(402);
                     } else if (var58.a[0] == 0 && af.compareTo(GloftMMN.k(402)) == 0) {
                        af = GloftMMN.k(417);
                     }

                     var58.a[0] = (var58.a[0] + 1) % 2;
                     var15 = ((var14 = 7 + var58.a[0] * 2) - 7) / 2;
                     var62.d = o[var15];

                     for(var56 = 4; var56 <= 10; var56 += 2) {
                        var58.a[var56] = 0;
                        var58.a[var56 + 1] = 0;
                     }

                     var58.a[4] = 0;
                     var58.a[5] = 9;
                     var58.a[9] = 17;
                     var58.a[7] = 41;
                     var93 = true;
                  }
               } else if (i == 1) {
                  var106 = 37;
                  var101 = var68.c(28);
               } else {
                  boolean var120;
                  if (i == 63) {
                     aI = true;
                     var93 = false;
                     char[][] var125 = new char[][]{{'A', 'B', 'C', 'D', 'E'}, {'F', 'G', 'H', 'I', 'J'}, {'K', 'L', 'M', 'N', 'O'}, {'P', 'Q', 'R', 'S', 'T'}, {'U', 'V', 'W', 'X', 'Y'}, {'Z', ' ', ' '}};
                     var106 = 37;
                     var101 = var68.c(28);
                     f.a(aB, false, true);
                     aB.setColor(16775323);
                     aB.fillRect(0, 70, 240, 36);
                     var120 = false;
                     boolean var121 = false;
                     aB.setClip(0, 160, 240, 201);
                     String var122 = " ";
                     boolean var130 = false;
                     short var132;
                     switch(var122.length()) {
                     case 4:
                        var132 = 198;
                        break;
                     case 5:
                        var132 = 205;
                        break;
                     case 6:
                        var132 = 212;
                        break;
                     case 7:
                        var132 = 220;
                        break;
                     default:
                        var132 = 205;
                     }

                     int var136;
                     if (aJ.length() <= 9) {
                        if (aI) {
                           aB.setColor(16777068);
                           aB.fillRect(0, 160, 240, 200);
                           var32 = 0;

                           while(true) {
                              if (var32 >= 6) {
                                 aB.setColor(0);
                                 M.a(aB, GloftMMN.k(937), var132, 330, 24, 1);
                                 break;
                              }

                              var33 = 160 + var32 * 28 + (var32 + 1) * 4;

                              for(var34 = 0; var34 < 5; ++var34) {
                                 var136 = 0 + var34 * 44 + (var34 + 1) * 4;
                                 if (var32 == 5 && var34 >= 3) {
                                    aB.setColor(3368601);
                                    aB.drawRoundRect(var136, var33, 86, 28, 8, 8);
                                    break;
                                 }

                                 if (var125[var32][var34] != ' ') {
                                    aB.setColor(3368601);
                                    aB.drawRoundRect(var136, var33, 42, 28, 8, 8);
                                    aB.setColor(0);
                                    M.a(aB, var125[var32][var34] + "", var136 + 24, var33 + 8, 24, 1);
                                 }
                              }

                              ++var32;
                           }
                        }

                        if (bB > -1 && bC > -1 && bB >= 4 && bB <= 244 && bC >= 160 && bC <= 358) {
                           var32 = (bB - 4) / 48;
                           var33 = (bC - 164) / 32;
                           af.length();
                           Object var134 = null;
                           char[] var135 = af.toCharArray();

                           for(var136 = 0; var136 < var135.length; ++var136) {
                              if (var135[var136] == '-') {
                                 if (var33 < var125.length && var32 < var125[var33].length) {
                                    if (var125[var33][var32] != ' ') {
                                       var135[var136] = var125[var33][var32];
                                    }
                                    break;
                                 }

                                 var135[var136] = ' ';
                                 break;
                              }
                           }

                           af = new String(var135);
                           af.trim();
                           bB = -1;
                           bC = -1;
                        }
                     }

                     if (bz > -1 && bA > -1 && bz >= 4 && bz <= 244 && bA >= 160 && bA <= 358) {
                        var32 = (bz - 4) / 48;
                        var33 = (bA - 164) / 32;
                        if (bz >= 148 && bz <= 236 && bA >= 325 && bA <= 358) {
                           if (4 * (var32 + 1) + 44 * var32 >= 195) {
                              --var32;
                           }

                           aB.setColor(0);
                           aB.drawRoundRect(4 * (var32 + 1) + 44 * var32, 160 + 4 * (var33 + 1) + 28 * var33, 88, 28, 8, 8);
                           aB.setColor(16755359);
                           aB.fillRoundRect(4 * (var32 + 1) + 44 * var32, 160 + 4 * (var33 + 1) + 28 * var33, 88, 28, 8, 8);
                           aB.setColor(0);
                           M.a(aB, GloftMMN.k(937), var132, 330, 24, 1);
                        } else if (var33 != 5 || var32 < 1) {
                           aB.setColor(0);
                           aB.drawRoundRect(4 * (var32 + 1) + 44 * var32, 160 + 4 * (var33 + 1) + 28 * var33, 44, 28, 8, 8);
                           aB.setColor(16755359);
                           aB.fillRoundRect(4 * (var32 + 1) + 44 * var32, 160 + 4 * (var33 + 1) + 28 * var33, 44, 28, 8, 8);
                           aB.setColor(0);
                           var34 = 0 + var32 * 44 + (var32 + 1) * 4;
                           var136 = 160 + var33 * 28 + (var33 + 1) * 4;
                           M.a(aB, var125[var33][var32] + "", var34 + 24, var136 + 8, 24, 1);
                        }
                     }

                     if (aC[13] < 0) {
                        if ((var32 = af.indexOf(45)) < 0) {
                           var32 = af.length();
                        }

                        --var32;
                        if (var32 >= 0) {
                           af.length();
                           Object var137 = null;
                           char[] var138;
                           (var138 = af.toCharArray())[var32] = '-';
                           af = new String(var138);
                        }
                     } else if (aC[14] < 0 || aP && aQ) {
                        if ((var32 = af.indexOf(45)) >= 0) {
                           af = af.substring(0, var32);
                        }

                        if (af.trim().length() <= 2) {
                           if (var58.a[0] == 1) {
                              af = GloftMMN.k(417);
                           } else {
                              af = GloftMMN.k(402);
                           }

                           i = 1;
                        } else {
                           i = 2;
                        }

                        aB.setColor(9806814);
                        aB.fillRect(0, 0, 240, 400);
                        var68.a = 30720;
                        var68.b = 51200;
                        var68.a(33);
                        var68.a(aB);
                        f.a(aB, true, true);
                        aB.setColor(9806814);
                        aB.fillRect(0, 70, 240, 36);
                        aB.setColor(9806814);
                        aB.fillRect(0, 139, 237, 127);
                        aI = false;
                        r();
                        var78 = true;
                     }
                  } else if (i == 2) {
                     var106 = 38;
                     var101 = var68.c(32);
                     if (aP) {
                        var26 = 1 + p[var15][5].a / r[var15][5];
                        if (var58.a[0] == 0) {
                           var26 += g.X.length - 1;
                        } else {
                           var26 += g.Y.length - 1;
                        }

                        --var26;
                        if (var58.a[0] == 0) {
                           --var26;
                        }

                        var58.a[4] = GloftMMN.c(0, var26);
                        var58.a[5] = GloftMMN.c(0, p[var15][5].l);
                        var120 = false;
                        var58.a[6] = GloftMMN.c(0, a[var15].length / 3 + -4);
                        var58.a[7] = GloftMMN.c(0, p[var15][2].l);
                        var58.a[8] = GloftMMN.c(0, p[var15][4].a / r[var15][4]);
                        var58.a[9] = GloftMMN.c(0, p[var15][4].l);
                        var93 = true;
                     }
                  } else if (i == 3) {
                     var106 = 39;
                     var101 = var68.c(34);
                     if (var16) {
                        var10002 = var58.a[4]--;
                     }

                     if (var83) {
                        var10002 = var58.a[4]++;
                     }

                     byte var115 = -2;
                     if (var58.a[0] == 0) {
                        var115 = -3;
                     }

                     if (var58.a[4] < 0) {
                        var58.a[4] = var114 + var115;
                     }

                     if (var58.a[4] > var114 + var115) {
                        var58.a[4] = 0;
                     }

                     if (var16 || var83) {
                        var93 = true;
                     }
                  } else if (i == 4) {
                     var106 = 39;
                     var10000 = var101 = var68.c(34);
                     var10000[1] += 21;
                     var26 = a[var15].length / 3;
                     var26 -= 4;
                     if (var16) {
                        var10002 = var58.a[6]--;
                     }

                     if (var83) {
                        var10002 = var58.a[6]++;
                     }

                     if (var58.a[6] < 0) {
                        var58.a[6] = var26 - 1;
                     }

                     if (var58.a[6] > var26 - 1) {
                        var58.a[6] = 0;
                     }

                     if (var16 || var83) {
                        var93 = true;
                     }
                  } else if (i == 5) {
                     var106 = 39;
                     var10000 = var101 = var68.c(34);
                     var10000[1] += 42;
                     if (var16) {
                        var10002 = var58.a[8]--;
                     }

                     if (var83) {
                        var10002 = var58.a[8]++;
                     }

                     if (var58.a[8] < -1) {
                        var58.a[8] = var114 - 2;
                     }

                     if (var58.a[8] >= var114 - 1) {
                        var58.a[8] = -1;
                     }

                     if (var16 || var83) {
                        var93 = true;
                     }
                  } else if (i == 6) {
                     var106 = 42;
                     var101 = var68.c(34);
                     var26 = p[var15][5].l;
                     if (var16) {
                        var10002 = var58.a[5]--;
                     }

                     if (var83) {
                        var10002 = var58.a[5]++;
                     }

                     if (var58.a[5] < 0) {
                        var58.a[5] = var26 - 1;
                     }

                     if (var58.a[5] > var26 - 1) {
                        var58.a[5] = 0;
                     }

                     if (var16 || var83) {
                        var93 = true;
                     }
                  } else if (i == 7) {
                     var106 = 42;
                     var10000 = var101 = var68.c(34);
                     var10000[1] += 21;
                     var26 = p[var15][2].l;
                     if (var16) {
                        var10000 = var58.a;
                        var10000[7] -= 8;
                     }

                     if (var83) {
                        var10000 = var58.a;
                        var10000[7] += 8;
                     }

                     if (var58.a[7] < 1) {
                        var58.a[7] = var26 - 8 + 1;
                     }

                     if (var58.a[7] > var26 - 1) {
                        var58.a[7] = 1;
                     }

                     if (var16 || var83) {
                        var93 = true;
                     }
                  } else if (i == 8) {
                     var106 = 42;
                     var10000 = var101 = var68.c(34);
                     var10000[1] += 42;
                     var26 = p[var15][4].l;
                     if (var16) {
                        var10000 = var58.a;
                        var10000[9] -= 8;
                     }

                     if (var83) {
                        var10000 = var58.a;
                        var10000[9] += 8;
                     }

                     if (var58.a[9] < 1) {
                        var58.a[9] = var26 - 8 + 1;
                     }

                     if (var58.a[9] > var26 - 1) {
                        var58.a[9] = 1;
                     }

                     if (var16 || var83) {
                        var93 = true;
                     }
                  } else if (i == 9) {
                     var106 = 45;
                     var101 = var68.c(41);
                     if (var16) {
                        var10002 = var58.a[12]--;
                     }

                     if (var83) {
                        var10002 = var58.a[12]++;
                     }

                     if (var58.a[12] < 1) {
                        var58.a[12] = W.length - 1;
                     }

                     if (var58.a[12] >= W.length) {
                        var58.a[12] = 1;
                     }

                     if (var16 || var83) {
                        var56 = var58.a[9] / 8;
                        var58.a[9] = var56 * 8 + var58.a[12];
                        var56 = var58.a[7] / 8;
                        var58.a[7] = var56 * 8 + var58.a[12];
                        var93 = true;
                     }
                  }
               }

               if (i != 63 && (var90 || var91 || var93 || var78)) {
                  var68.a = 30720;
                  var68.b = 51200;
                  var68.a(33);
                  var68.a(aB);
               }

               if (!var78) {
                  if (var101 != null && var106 != 39 && var106 != 42) {
                     aB.setClip(0, 0, 240, 400);
                     var68.a = var101[0] << 8;
                     var68.b = var101[1] << 8;
                     var68.e = -1;
                     var68.a(var106);
                     var68.a(aB);
                     var68.c();
                     if (i == 0 || i == 9) {
                        if (i == 0) {
                           var74.a(36);
                        } else {
                           var74.a(46);
                        }

                        var74.a = var68.a;
                        var74.b = var68.b;
                        var74.a(aB);
                        var74.b();
                        var54 = var74.c();
                     }
                  }

                  aB.setClip(0, 0, 240, 400);
                  if (var93) {
                     var26 = 0;

                     while(true) {
                        if (var26 >= p.length) {
                           var58.a(var14, 0, true);
                           break;
                        }

                        for(var27 = 0; var27 < p[var26].length; ++var27) {
                           if (p[var15][var27] != null) {
                              p[var15][var27].w = (Image[][])null;
                           }
                        }

                        ++var26;
                     }
                  }

                  if (i != 63) {
                     var68.a = 30720;
                     var68.b = 51200;
                     if (var106 == 39) {
                        var68.a(41);
                     } else {
                        var68.a(34);
                     }

                     var68.a(aB);
                  }

                  if ((var106 == 39 || var106 == 42) && var101 != null) {
                     aB.setClip(0, 0, 240, 400);
                     if (var106 == 42) {
                        var68.a(44);
                        var68.a(aB);
                     }

                     var68.a = var101[0] << 8;
                     var68.b = var101[1] << 8;
                     var68.e = -1;
                     var68.a(var106);
                     var68.a(aB);
                     var74.a = var68.a;
                     var74.b = var68.b;
                     if (var106 == 39) {
                        var74.a(40);
                     } else {
                        var74.a(43);
                     }

                     var74.a(aB);
                     var74.b();
                     var54 = var74.c();
                  }
               }
            }
         }

         if (i != 63 && aC[14] < 0 && g.ak == null) {
            g.a(GloftMMN.k(866), (byte)-56);
            i = 0;
         }

         a(var54[0] >> 8, var54[1] >> 8, var54[2] - var54[0] >> 8 >> 1, var54[3] - var54[1] >> 8, 1, 52, 2, 52);
         a((var54[0] >> 8) + (var54[2] - var54[0] >> 8 >> 1), var54[1] >> 8, (var54[2] - var54[0] >> 8 >> 1) + 40, var54[3] - var54[1] >> 8, 1, 54, 2, 54);
         return;
      case 18:
         if (v[13] == null && y != 66 && y != 36) {
            GloftMMN.b("/10");
            a(4, u, 4, true, true, 1, true);
            GloftMMN.f();
            v[13] = new d(u[4], 0, 0, (d)null);
            v[14] = new d(u[4], 0, 0, (d)null);
            v[15] = new d(u[4], 0, 0, (d)null);
            v[16] = new d(u[4], 0, 0, (d)null);
         }

         var6 = v[13];
         var7 = v[15];
         var8 = v[16];
         g var9 = s[0];
         var10 = false;
         if (var6 != null) {
            var6.a = 30720;
            var6.b = 51200;
         }

         var11 = true;
         if (y == 40 || y == 59 || y == 62) {
            if (aC[3] < 0) {
               bF[1] = 0;
               bF[2] = 0;
               bF[5] = 2;
            } else if (aC[4] < 0) {
               bF[1] = 0;
               bF[2] = 1;
               bF[5] = 2;
            }

            if (y == 62) {
               bF[4] = s[0].b[1] - 1;
            } else if (bF[2] == 0) {
               if (s[0].a[0] == 0) {
                  bF[4] = 43;
               } else {
                  bF[4] = 48;
               }
            } else if (s[0].a[0] == 0) {
               bF[4] = 12;
            } else {
               bF[4] = 15;
            }

            if (y != 59) {
               var10002 = bF[4]++;
            }

            if (y == 59) {
               ac = 0;

               for(var5 = 0; var5 < bF[4]; ++var5) {
                  if (bF[2] == 0) {
                     var12 = g.b(var5, -1);
                  } else {
                     var12 = g.b(-1, var5);
                  }

                  if (var12 != -1) {
                     ab[ac++] = var5;
                  }
               }

               bF[4] = ac + 1;
            }

            if (aC[1] <= 2 && aC[1] != 1 && aO != 1) {
               if (aC[2] <= 2 && aC[2] != 1 && aO != 2) {
                  if (i != 0 && aC[3] >= 0 && aC[4] >= 0) {
                     var11 = false;
                  }
               } else {
                  aO = 0;
                  if (bF[1] < bF[4] - 1) {
                     bF[5] = 0;
                     var10002 = bF[1]++;
                  }
               }
            } else {
               aO = 0;
               if (bF[1] > 0) {
                  bF[5] = 0;
                  var10002 = bF[1]--;
               }
            }

            var71 = false;
            if (i == 0 || k) {
               h = 16;
               f.a(aB, true, true);
               h = 18;
               f.c(aB, false);
               f.d(aB, false);
               i = 1;
               var71 = true;
            }

            bF[3] = bF[1] / 9;
            if (var11 || bF[5] == 2 || var71) {
               if (y == 59) {
                  var6.a(2);
               } else if (y == 62) {
                  var6.a(1);
               } else {
                  var6.a(0);
               }

               var6.a(aB);
            }

            if (y != 62) {
               var6.a(4 + bF[2] * 2);
               var6.a(aB);
            }

            int[] var13;
            if (y != 62) {
               var7.a = 30720;
               var7.b = 51200;
               var7.a(4 + bF[2] * 2 + 1);
               a((var13 = var7.c())[0] >> 8, (var13[1] >> 8) - 20, var13[2] - var13[0] >> 1 >> 8, (var13[3] - var13[1] >> 8) + 40, 1, 52, 2, 52);
               a(var13[0] + var13[2] >> 1 >> 8, (var13[1] >> 8) - 20, var13[2] - var13[0] >> 1 >> 8, (var13[3] - var13[1] >> 8) + 40, 1, 54, 2, 54);
               var7.a(aB);
               var7.b();
            }

            var8.a = 30720;
            var8.b = 51200;
            var8.a(8);
            if (a(((var13 = var8.c())[0] >> 8) - 20, var13[1] >> 8, (var13[2] - var13[0] >> 8) + 40, var13[3] - var13[1] >> 1 >> 8, true)) {
               this.keyPressed(50);
               this.keyReleased(50);
            }

            if (a((var13[0] >> 8) - 20, var13[1] + var13[3] >> 1 >> 8, (var13[2] >> 8) + 40, var13[3] - var13[1] >> 1 >> 8, true)) {
               this.keyPressed(56);
               this.keyReleased(56);
            }

            var8.a(aB);
            var8.b();
            var10002 = bF[5]++;
         }

         var12 = -1;
         var73 = -1;
         var14 = -1;
         var15 = -1;
         var16 = false;
         var17 = -1;
         var18 = null;
         if (y == 47 || y == 48 || y == 49 || y == 50) {
            var19 = bF[12];
            f.au = ad[var19 * 2 + 0];
            var18 = f.c[f.au];
            var73 = ad[bF[12] * 2 + 1];
            var15 = ((var12 = f[var73 - 1] & 255) & 240) >> 4;
            var18.d.m = var15;
            bF[14] = var15;
            var14 = (var12 & 15) >> 0;
            var18.a(f.h(f.e[f.au][0], var14));
            var18.c();
            short var79 = -1;

            for(var5 = 0; var5 < 64 && (var21 = var5 * 17) < g.length; ++var5) {
               if (g[var21 + 2] == var73) {
                  var17 = var5;
                  var79 = g[var21 + 3 + var14];
                  break;
               }
            }

            bF[19] = var79;
            if (var18.d.w == null || var18.d.w[var15] == null) {
               var18.d.w = (Image[][])null;
               var18.d.a(var15, 0, -1, -1, var15);
            }
         }

         int[] var25;
         int var28;
         int var30;
         int var31;
         boolean var35;
         int var36;
         int var37;
         int var38;
         int var39;
         int var40;
         byte var85;
         byte[] var139;
         label3302:
         switch(y) {
         case 36:
            if (i == 0) {
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 400);
               M.a(aB, GloftMMN.k(829), 120, 200, 3, 0);
               ++i;
            } else {
               i = 0;
               if (J) {
                  f.m = null;
               }

               GloftMMN.b("/10");
               a(4, u, 4, true, true, 1, true);
               GloftMMN.f();
               v[13] = new d(u[4], 0, 0, (d)null);
               v[14] = new d(u[4], 0, 0, (d)null);
               v[15] = new d(u[4], 0, 0, (d)null);
               v[16] = new d(u[4], 0, 0, (d)null);
               y = z;
               c[0] = 0;
               I = H;
            }
            break;
         case 37:
            aB.setColor(0);
            aB.fillRect(0, 0, 240, 400);
            a(3, 0, false);

            for(var5 = 0; var5 < 3; ++var5) {
               R[var5] = 815 + var5;
            }

            U[2] = 818;
            U[3] = 819;
            U[5] = -1;
            i = 0;
            y = 38;
            break;
         case 38:
            if (k) {
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 400);
            }

            a(false);
            a(aB, false, false);
            if (aP) {
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 400);
               M.a(aB, GloftMMN.k(829), 120, 200, 3, 0);
               if ((var19 = U[0]) == 0) {
                  y = 39;
               } else {
                  bF[20] = H;
                  i = -1;
                  y = 45;
                  if (var19 == 1) {
                     z = 46;
                  } else {
                     z = 52;
                  }
               }
            }
            break;
         case 39:
         case 58:
         case 61:
            GloftMMN.n = null;
            GloftMMN.o = null;
            f.b();
            g.a(false);
            GloftMMN.a("/3", 0);
            if (y == 58) {
               y = 59;
            } else if (y == 61) {
               bF[15] = var9.a[4];
               bF[16] = var9.a[5];
               y = 62;
            } else {
               y = 40;
            }

            bF[0] = -1;
            bF[1] = 0;
            bF[2] = 0;
            bF[5] = 2;
            i = 0;
            break;
         case 40:
            if (aP) {
               y = 41;
               byte var70 = 7;
               if (bF[2] == 1) {
                  var70 = 9;
               }

               bF[10] = s[0].a[var70];
            }
            break;
         case 41:
         case 60:
         case 64:
            d var89 = v[14];
            var20 = bF[1] - bF[3] * 9;
            bF[0] = var20;
            var89.a(12);
            var89.a = bF[6] << 8;
            var89.b = bF[7] << 8;
            var89.a(aB);
            var89.b();
            bF[0] = -1;
            if (y != 64) {
               var6.a(aB);
            }

            var8.a(aB);
            var8.b();
            if (var89.a()) {
               if (y == 64) {
                  if (var9.a[35] >= 10) {
                     y = 63;
                     i = 0;
                  } else {
                     y = 57;
                     z = 62;
                     bB = -1;
                     g.a((int)434);
                  }
               } else if (y == 60) {
                  var9.a[8] = bF[11];
                  var9.a[9] = bF[12];
                  var9.a[6] = bF[13];
                  var9.a[7] = bF[14];
                  bF[1] = 0;
                  y = 59;
                  i = 0;
                  f.bc[14] = 1;
               } else {
                  var85 = b[var9.a[0]][bF[2]][bF[1]];
                  if (var9.a[35] >= var85) {
                     y = 42;
                     i = 0;
                  } else {
                     y = 57;
                     z = 40;
                     g.a((int)434);
                     bB = -1;
                  }
               }
            }
            break;
         case 42:
            var11 = true;
            var21 = bF[10] / 8;
            var22 = bF[10] % 8;
            if (aC[3] < 0) {
               --var21;
            } else if (aC[4] < 0) {
               ++var21;
            } else {
               var11 = false;
            }

            if (bF[2] == 1) {
               var23 = p[var9.a[0]][4];
            } else {
               var23 = p[var9.a[0]][2];
            }

            int var24 = var23.l / 8;
            if (var21 >= var24) {
               var21 = 0;
            }

            if (var21 < 0) {
               var21 = var24 - 1;
            }

            bF[10] = var21 * 8 + var22;
            if (var11 || i == 0) {
               bF[5] = 2;
               i = 1;
               var6.a(0);
               var6.a(aB);
            }

            var6.a(4 + bF[2] * 2);
            var6.a(aB);
            var8.a(15);
            a((var25 = var8.c())[0] >> 8, (var25[1] >> 8) - 20, var25[2] - var25[0] >> 1 >> 8, (var25[3] - var25[1] >> 8) + 40, 1, 52, 2, 52);
            a(var25[0] + var25[2] >> 1 >> 8, (var25[1] >> 8) - 20, var25[2] - var25[0] >> 1 >> 8, (var25[3] - var25[1] >> 8) + 40, 1, 54, 2, 54);
            var8.a(aB);
            var8.b();
            if (aP) {
               y = 43;
               i = -1;
            }
            break;
         case 43:
            var85 = b[var9.a[0]][bF[2]][bF[1]];
            if (i == -1) {
               g.a(GloftMMN.k(865) + " $" + f.e(var85), (byte)-56);
               i = 0;
            } else {
               a(aB);
               if (aP) {
                  if (i == 0) {
                     var10 = true;
                     y = 42;
                  } else if (i == 1) {
                     var28 = bF[1] - 1;
                     if (bF[2] == 0) {
                        g.a((int)var28, (int)-1, (byte)((byte)bF[10]));
                     } else {
                        g.a((int)-1, (int)var28, (byte)((byte)bF[10]));
                     }

                     y = 56;
                     z = 44;
                     i = -1;
                     K = 0;
                     f.bo[70] = var9.a[35];
                     f.bo[71] = var9.a[35] - var85;
                  }
               }
            }
            break;
         case 44:
            if (i == -1) {
               g.a(GloftMMN.k(864), (byte)-56);
               i = 0;
            } else {
               a(aB);
               if (aP) {
                  if (i != 0 && i == 1) {
                     var9.a[8] = bF[11];
                     var9.a[9] = bF[12];
                     var9.a[6] = bF[13];
                     var9.a[7] = bF[14];
                     f.bc[14] = 1;
                  }

                  bF[1] = 0;
                  y = 40;
                  i = 0;
                  var10 = true;
               }
            }
            break;
         case 45:
            if (i == -1) {
               aB.setColor(0);
               aB.fillRect(0, 0, 240, 400);
               a(2, 0, true);
               U[2] = 830;
               U[3] = 831;
               R[0] = 832;
               R[1] = 833;
               i = 0;
            } else {
               a(false);
               a(aB, false, false);
               if (aP) {
                  aB.setColor(0);
                  aB.fillRect(0, 0, 240, 400);
                  M.a(aB, GloftMMN.k(829), 120, 200, 3, 0);
                  if ((var28 = U[0]) == 0) {
                     if (f.bc[0] >= 30) {
                        bF[11] = 22;
                     } else {
                        bF[11] = 0;
                     }

                     f.bd = 0;
                     y = z;
                  } else if (var28 == 1) {
                     if (f.bc[0] >= 30) {
                        bF[11] = 34;
                     } else {
                        bF[11] = 1;
                     }

                     f.bd = 0;
                     y = z;
                  } else {
                     y = 66;
                  }
               }
            }
            break;
         case 46:
            H = bF[11];
            if (f.bd == 0) {
               g.M = GloftMMN.k(829);
            }

            if (!f.a(aB, H)) {
               if (f.bd == 1) {
                  aB.setClip(0, 0, 240, 400);
                  M.a(aB, g.M, 120, 200, 3, 0);
               }
               break;
            }

            y = 47;
            f.d = true;
            bF[0] = -1;
            bF[12] = 0;
            bF[13] = 0;

            int var119;
            for(var28 = 0; var28 < 64 && (var119 = var28 * 17) < g.length; ++var28) {
               short var127 = g[var119 + 0];
               short var131 = g[var119 + 1];

               for(var5 = 0; var5 < f.f; ++var5) {
                  if ((f.e[var5][0] & 255) == var131 && H == var127 && g[var119 + 2] == f.e[var5][10]) {
                     var34 = bF[13];
                     ad[var34 * 2 + 0] = var5;
                     ad[var34 * 2 + 1] = g[var119 + 2];
                     var10002 = bF[13]++;
                  }
               }
            }

            h = 16;
            f.a(aB, true, true);
            h = 18;
            f.c(aB, false);
            f.d(aB, false);
            g.M = null;
            break;
         case 47:
            short var116 = f.e[f.au][1];
            short var118 = f.e[f.au][2];
            f.b(var116, var118, var9.c);
            a(var9);
            f.a(aB);
            f.d = false;
            var18.a = var116 - f.C + 0 << 8;
            var18.b = var118 - f.D + 75 << 8;
            var25 = var18.c();
            d var126;
            (var126 = v[2]).a = var25[2] + var25[0] >> 1;
            var126.b = var25[1];
            var126.a(27);
            var126.a(aB);
            var126.b();
            if (aG % 4L < 2L && var25[2] - var25[0] != 0) {
               var25 = var18.c();
               aB.setColor(16711680);
               aB.drawRect(var25[0] >> 8, var25[1] >> 8, var25[2] - var25[0] >> 8, var25[3] - var25[1] >> 8);
            }

            if (aC[3] < 0) {
               f.E = -1000;
               f.d = true;
               var10002 = bF[12]--;
            }

            if (aC[4] < 0) {
               f.E = -1000;
               f.d = true;
               var10002 = bF[12]++;
            }

            if (bF[12] < 0) {
               bF[12] = bF[13] - 1;
            }

            if (bF[12] >= bF[13]) {
               bF[12] = 0;
            }

            if (aC[13] < 0 || aC[11] < 0) {
               GloftMMN.c(3);
               y = 45;
               z = 46;
               i = -1;
            }

            if (aP) {
               bF[18] = var12;
               y = 48;
            }

            b();
            if (var25[2] - var25[0] >> 8 > 0) {
               a(var25[0] >> 8, var25[1] >> 8, var25[2] - var25[0] >> 8, var25[3] - var25[1] >> 8, 1, 53, 2, 53);
            } else {
               aB.setColor(255, 0, 0);
               aB.drawRect((var25[0] >> 8) - 35, var25[1] >> 8, 70, 30);
               a((var25[0] >> 8) - 35, var25[1] >> 8, 70, 30, 1, 53, 2, 53);
            }

            a(var126);
            break;
         case 48:
            var28 = f.b(f.e[f.au][0], 3, -1);

            while(g[var17 * 17 + 3 + var14] < 0) {
               ++var14;
               if (var14 >= var28) {
                  var14 = 0;
               }
            }

            var6.a(16);
            var6.a(aB);
            var7.a = var6.a;
            var7.b = var6.b;
            var7.a(18);
            b();
            a(var7);
            b(0, 0, 240, 360, 3, 0);
            var7.a(aB);
            var7.b();
            var6.a(19);
            var6.a(aB);
            if (aC[13] < 0) {
               GloftMMN.c(3);
               f[var73 - 1] = (byte)bF[18];
               y = 47;
            } else {
               if (!aP && aC[14] >= 0) {
                  if (aC[3] < 0) {
                     do {
                        --var14;
                        if (var14 < 0) {
                           var14 = var28 - 1;
                        }
                     } while(g[var17 * 17 + 3 + var14] < 0);
                  }

                  if (aC[4] < 0) {
                     do {
                        ++var14;
                        if (var14 >= var28) {
                           var14 = 0;
                        }
                     } while(g[var17 * 17 + 3 + var14] < 0);
                  }

                  var139 = f;
                  var139[var73 - 1] = (byte)(var139[var73 - 1] & 240);
                  var139 = f;
                  var139[var73 - 1] = (byte)(var139[var73 - 1] | var14 << 0);
                  break;
               }

               y = 49;
            }
            break;
         case 49:
            var6.a(16);
            var6.a(aB);
            var6.a(19);
            var6.a(aB);
            var7.a = var6.a;
            var7.b = var6.b;
            var7.a(20);
            b();
            a(var7);
            b(0, 0, 240, 360, 3, 0);
            var7.a(aB);
            var7.b();
            boolean var29 = false;
            if (aC[3] < 0 || aC[4] < 0) {
               if (aC[3] < 0) {
                  --var15;
               }

               if (aC[4] < 0) {
                  ++var15;
               }

               if (var15 >= var18.d.l / 3) {
                  var15 = 0;
               } else if (var15 < 0) {
                  var15 = var18.d.l / 3 - 1;
               }

               var139 = f;
               var139[var73 - 1] = (byte)(var139[var73 - 1] & 15);
               var139 = f;
               var139[var73 - 1] = (byte)(var139[var73 - 1] | var15 << 4);
               byte[] var124 = new byte[var18.d.w.length];
               var18.d.a(var15, 0, -1, -1, var15);
               var124[var15] = 1;

               for(var5 = 0; var5 < f.c.length; ++var5) {
                  d var129;
                  short var133;
                  if ((var129 = f.c[var5]) != null && var129.d == var18.d && (var133 = f.e[var5][4]) >= 0) {
                     var124[var133] = 1;
                  }
               }

               for(var5 = 0; var5 < var18.d.w.length; ++var5) {
                  if (var124[var5] == 0) {
                     var18.d.w[var5] = null;
                  }
               }

               System.gc();
            }

            if (aC[13] < 0) {
               GloftMMN.c(3);
               f[var73 - 1] = (byte)bF[18];
               y = 48;
            }

            if (aP || aC[14] < 0) {
               y = 50;
               i = -1;
            }
            break;
         case 50:
            var80 = bF[19];
            if (var9.a[35] < var80) {
               if (i == -1) {
                  g.a((int)434);
                  i = 0;
               }

               g.b(aB);
               if (aP) {
                  f[var73 - 1] = (byte)bF[18];
                  g.N.removeAllElements();
                  y = 47;
               }
            } else if (i == -1) {
               g.a(GloftMMN.k(865) + " $" + f.e(var80), (byte)-56);
               i = 0;
            } else {
               a(aB);
               if (aP) {
                  if (i == 0) {
                     f[var73 - 1] = (byte)bF[18];
                     y = 47;
                  } else if (i == 1) {
                     var10000 = var9.a;
                     var10000[35] -= var80;
                     y = 47;
                     short[] var123;
                     (var123 = f.e[f.au])[4] = (short)var18.d.m;
                     f.ae[f.au] = (byte)f.b(var123[0], 1, var123[3]);
                     f.af[f.au] = (byte)f.b(var123[0], 2, var123[3]);
                     var18.a = var123[1] << 8;
                     var18.b = var123[2] << 8;
                     int[] var128 = var18.c();
                     f.ag[f.au][0] = var128[0] >> 8;
                     f.ag[f.au][1] = var128[1] >> 8;
                     f.ag[f.au][2] = var128[2] >> 8;
                     f.ag[f.au][3] = var128[3] >> 8;
                     f.d(aB, false);
                     f.E = -1000;
                     f.d = true;
                     k = true;
                  }
               }
            }
         case 51:
         default:
            break;
         case 52:
            H = bF[11];
            if (f.a(aB, H)) {
               y = 53;
               f.d = true;
               bF[0] = -1;
               h = 16;
               f.a(aB, true, true);
               h = 18;
               f.c(aB, false);
               f.d(aB, false);
               a(var9);
               f.a(aB);
               f.d = false;
               var30 = f.bh * 4;
               bF[11] = bM[var30 + 0] & 255;
               bF[12] = bM[var30 + 1] & 255;
               bF[13] = bM[var30 + 2] & 255;
               bF[14] = bM[var30 + 3] & 255;
               bF[15] = 0;
               i = 0;
            }
            break;
         case 53:
            if (i == -1) {
               f.d = true;
               f.a(aB);
               f.d = false;
               i = 0;
            }

            if (k) {
               h = 16;
               f.a(aB, true, true);
               h = 18;
               f.c(aB, false);
               f.d(aB, false);
               f.d = true;
               f.a(aB);
               f.d = false;
            }

            var6.a(16);
            var6.a(aB);
            var6.a(19);
            var6.a(aB);
            var7.a = var6.a;
            var7.b = var6.b;
            if (bF[15] == 1) {
               var7.a(20);
            } else {
               var7.a(17);
            }

            b();
            a(var7);
            b(0, 0, 240, 360, 3, 0);
            var7.a(aB);
            var7.b();
            var30 = f.bh * 4;
            var31 = bM[var30 + 0] & 255;
            var32 = bM[var30 + 1] & 255;
            if (aC[13] < 0) {
               GloftMMN.c(3);
               if (bF[15] == 0) {
                  y = 45;
                  z = 52;
                  i = -1;
                  bM[var30 + 0] = (byte)bF[11];
                  bM[var30 + 1] = (byte)bF[12];
               } else {
                  bF[15] = 0;
               }
            } else if (!aP && aC[14] >= 0) {
               var33 = -1;
               var34 = bN.length / 23;

               for(var5 = 0; var5 < bN.length; var5 += 23) {
                  if (bN[var5 + 0] == var31) {
                     var33 = var5 / 23;
                     break;
                  }
               }

               bF[16] = bN[var33 * 23 + 1];
               var35 = true;
               var36 = -1;
               var37 = -1;
               if (bF[15] == 1) {
                  var38 = -1;
                  var39 = 0;

                  for(var5 = 0; var5 < 20; ++var5) {
                     if ((var40 = bN[var33 * 23 + 3 + var5]) >= 0) {
                        ++var39;
                     }

                     if (var32 == var40) {
                        var38 = var5;
                     }
                  }

                  if (aC[3] < 0) {
                     --var38;
                  } else if (aC[4] < 0) {
                     ++var38;
                  } else {
                     var35 = false;
                  }

                  if (var35) {
                     var36 = var31;
                     var37 = var32;
                     if (var38 < 0) {
                        var38 = var39 - 1;
                     }

                     if (var38 >= var39) {
                        var38 = 0;
                     }

                     var32 = bN[var33 * 23 + 3 + var38];
                     bM[var30 + 1] = (byte)var32;
                  }
               } else {
                  if (aC[3] < 0) {
                     --var33;
                  } else if (aC[4] < 0) {
                     ++var33;
                  } else {
                     var35 = false;
                  }

                  if (var33 < 0) {
                     var33 = var34 - 1;
                  }

                  if (var33 >= var34) {
                     var33 = 0;
                  }

                  if (var35) {
                     var36 = var31;
                     var37 = var32;
                     var31 = bN[var33 * 23 + 0];
                     var32 = bN[var33 * 23 + 2];
                     bM[var30 + 0] = (byte)var31;
                     if (var31 == bF[11]) {
                        bM[var30 + 1] = (byte)bF[12];
                     } else {
                        bM[var30 + 1] = (byte)var32;
                     }
                  }
               }

               if (!var35) {
                  break;
               }

               var5 = 0;

               while(true) {
                  if (var5 >= 2) {
                     break label3302;
                  }

                  if (var36 != -1 && var37 != -1) {
                     f.b[0].w[var37][var36 + var5] = null;
                  }

                  f.b[0].a(bM[var30 + 1] & 255, (bM[var30 + 0] & 255) + var5, (bM[var30 + 0] & 255) + var5, -1, bM[var30 + 1] & 255);
                  System.gc();
                  ++var5;
               }
            } else if (bF[15] == 0) {
               bF[15] = 1;
            } else {
               bF[15] = 0;
               y = 54;
            }
            break;
         case 54:
            var6.a(16);
            var6.a(aB);
            var6.a(19);
            var6.a(aB);
            if (bF[15] == 1) {
               var7.a(20);
            } else {
               var7.a(18);
            }

            var7.a(aB);
            var7.b();
            var30 = f.bh * 4;
            var31 = bM[var30 + 2] & 255;
            var32 = bM[var30 + 3] & 255;
            if (aC[13] < 0) {
               GloftMMN.c(3);
               if (bF[15] == 0) {
                  bF[15] = 1;
                  y = 53;
               } else {
                  bF[15] = 0;
               }
            } else if (aP) {
               if (bF[15] == 0) {
                  bF[15] = 1;
               } else {
                  y = 55;
                  i = -1;
               }
            } else {
               var33 = -1;
               var34 = bO.length / 22;

               for(var5 = 0; var5 < bO.length; var5 += 22) {
                  if (bO[var5 + 0] == var31) {
                     var33 = var5 / 22;
                     break;
                  }
               }

               var35 = true;
               var36 = -1;
               var37 = -1;
               if (bF[15] == 0) {
                  if (aC[3] < 0) {
                     --var33;
                  } else if (aC[4] < 0) {
                     ++var33;
                  } else {
                     var35 = false;
                  }

                  if (var33 < 0) {
                     var33 = var34 - 1;
                  }

                  if (var33 >= var34) {
                     var33 = 0;
                  }

                  if (var35) {
                     var36 = var31;
                     var37 = var32;
                     var31 = bO[var33 * 22 + 0];
                     var32 = bO[var33 * 22 + 1];
                     bM[var30 + 2] = (byte)var31;
                     if (var31 == bF[13]) {
                        bM[var30 + 3] = (byte)bF[14];
                     } else {
                        bM[var30 + 3] = (byte)var32;
                     }
                  }
               } else {
                  var38 = -1;
                  var39 = 0;

                  for(var5 = 0; var5 < 20; ++var5) {
                     if ((var40 = bO[var33 * 22 + 2 + var5]) >= 0) {
                        ++var39;
                     }

                     if (var32 == var40) {
                        var38 = var5;
                     }
                  }

                  if (aC[3] < 0) {
                     --var38;
                  } else if (aC[4] < 0) {
                     ++var38;
                  } else {
                     var35 = false;
                  }

                  if (var35) {
                     var36 = var31;
                     var37 = var32;
                     if (var38 < 0) {
                        var38 = var39 - 1;
                     }

                     if (var38 >= var39) {
                        var38 = 0;
                     }

                     var32 = bO[var33 * 22 + 2 + var38];
                     bM[var30 + 3] = (byte)var32;
                  }
               }

               if (var35) {
                  if (var36 != -1 && var37 != -1) {
                     f.b[0].w[var37][var36] = null;
                  }

                  f.b[0].a(bM[var30 + 3] & 255, bM[var30 + 2] & 255, bM[var30 + 2] & 255, -1, bM[var30 + 3] & 255);
                  System.gc();
               }
            }
            break;
         case 55:
            var80 = bF[16];
            if (var9.a[35] < var80) {
               if (i == -1) {
                  g.a((int)434);
                  i = 0;
               }

               g.b(aB);
               if (aP) {
                  g.N.removeAllElements();
                  y = 53;
               }
            } else if (i == -1) {
               g.a(GloftMMN.k(865) + " $" + f.e(var80), (byte)-56);
               i = 0;
            } else {
               a(aB);
               if (aP) {
                  if (i == 0) {
                     y = 53;
                  } else if (i == 1) {
                     var10000 = var9.a;
                     var10000[35] -= var80;
                     y = 66;
                  }
               }
            }

            if (y != 53) {
               break;
            }

            i = -1;
            var30 = f.bh * 4;
            var33 = bM[var30 + 0] & 255;
            var34 = bM[var30 + 1] & 255;
            bM[var30 + 0] = (byte)bF[11];
            bM[var30 + 1] = (byte)bF[12];

            for(var5 = 0; var5 < 2; ++var5) {
               if (var33 != -1 && var34 != -1) {
                  f.b[0].w[var34][var33 + var5] = null;
               }

               f.b[0].a(bM[var30 + 1] & 255, (bM[var30 + 0] & 255) + var5, (bM[var30 + 0] & 255) + var5, -1, bM[var30 + 1] & 255);
            }

            var33 = bM[var30 + 2] & 255;
            var34 = bM[var30 + 3] & 255;
            bM[var30 + 2] = (byte)bF[13];
            bM[var30 + 3] = (byte)bF[14];
            if (var33 != -1 && var34 != -1) {
               f.b[0].w[var34][var33] = null;
            }

            f.b[0].a(bM[var30 + 3] & 255, bM[var30 + 2] & 255, bM[var30 + 2] & 255, -1, bM[var30 + 3] & 255);
            System.gc();
            break;
         case 56:
            var9.a[35] = f.bo[71];
            i = -1;
            y = z;
            z = -1;
            break;
         case 57:
            g.b(aB);
            if (bB > 0) {
               y = z;
               i = 0;
               bB = -1;
            }
            break;
         case 59:
            if (aP) {
               y = 60;
            }
            break;
         case 62:
            if (aP) {
               y = 64;
            }
            break;
         case 63:
            var11 = true;
            var26 = p[var9.a[0]][5].l;
            var27 = var9.a[5];
            if (aC[3] < 0) {
               --var27;
            } else if (aC[4] < 0) {
               ++var27;
            } else {
               var11 = false;
            }

            if (var27 < 0) {
               var27 = var26 - 1;
            }

            if (var27 >= var26) {
               var27 = 0;
            }

            var9.a[5] = var27;
            if (var11 || i == 0) {
               bF[5] = 2;
               i = 1;
               var6.a(1);
               var6.a(aB);
            }

            var8.a(15);
            a((var25 = var8.c())[0] >> 8, (var25[1] >> 8) - 20, var25[2] - var25[0] >> 1 >> 8, (var25[3] - var25[1] >> 8) + 40, 1, 52, 2, 52);
            a(var25[0] + var25[2] >> 1 >> 8, (var25[1] >> 8) - 20, var25[2] - var25[0] >> 1 >> 8, (var25[3] - var25[1] >> 8) + 40, 1, 54, 2, 54);
            var8.a(aB);
            var8.b();
            if (aP) {
               y = 65;
               i = -1;
            }

            if (aC[13] < 0 || aC[1] < 0 || aC[2] < 0) {
               GloftMMN.c(3);
               y = 62;
               aC[13] = 0;
               var9.a[5] = bF[16];
            }
            break;
         case 65:
            if (i == -1) {
               g.a(GloftMMN.k(865) + " $" + f.e(10), (byte)-56);
               i = 0;
            } else {
               a(aB);
               if (aP) {
                  if (i == 0) {
                     var9.a[5] = bF[16];
                     y = 63;
                  } else if (i == 1) {
                     y = 66;
                  }
               }
            }
         }

         if (var6 != null && !var10 && i != 0 && (y == 59 || y == 62 || y == 63 || y > 39 && y < 43)) {
            var5 = var6.e;
            var6.a(3);
            var6.a(aB);
            var6.a(var5);
         }

         if (y != 40 && y != 59 && y != 62) {
            if ((y == 42 || y == 63) && (aC[13] < 0 || aC[1] < 0 || aC[2] < 0)) {
               GloftMMN.c(3);
               if (y == 42) {
                  y = 40;
                  i = 0;
               } else {
                  y = 62;
               }
            }
         } else if (aC[13] < 0) {
            GloftMMN.c(3);
            if (y != 40 && y != 59) {
               if (y == 62) {
                  var9.a[4] = bF[15];
                  var9.a[5] = bF[16];
               }
            } else {
               g.h();
            }

            y = 66;
         }

         if (y == 66) {
            if (u[4] != null) {
               i = 0;
               u[4] = null;
               v[13] = null;
               v[14] = null;
               v[15] = null;
               v[16] = null;
            } else if (i == 1) {
               g.a(true);
            } else if (i == 2) {
               p[0] = null;
               p[1] = null;
            } else if (i == 3) {
               f.e();
            } else if (i == 4) {
               if (f.b[0] != null) {
                  f.b[0].b();
                  f.b[0].c();
                  f.b[0] = null;
               }
            } else if (i == 5) {
               for(var5 = 3; var5 < f.b.length; ++var5) {
                  if (f.b[var5] != null) {
                     f.b[var5].b();
                     f.b[var5].c();
                     f.b[var5] = null;
                  }
               }
            } else if (i == 6) {
               f.b();
            } else if (i == 7) {
               H = I;
               a(8);
               y = 20;
               z = -1;
               L = 0;
               f.bd = 0;
            }

            if (i != 7) {
               ++i;
               return;
            }
         }
         break;
      case 19:
         if (d(31, 2)) {
            a(6);
            i = -1;
            return;
         }
         break;
      case 20:
         if (e(42, 2)) {
            a(19);
            d(31, 1);
            return;
         }
         break;
      case 21:
         if (i == -1) {
            i = 800;
            y = -1;
            L = 0;
            K = 0;
            GloftMMN.a("/3", 2);
            GloftMMN.f();
            f.m = Image.createImage(240, 400);
            f.aj = f.m.getGraphics();
            GloftMMN.e();
         }

         aB.setClip(0, 0, 240, 400);
         aB.setColor(0);
         aB.fillRect(0, 0, 240, 400);
         String var51 = null;
         if (i >= 400 + K) {
            L = 0;
            i = 0;
            ++y;
            if (0 + y == 17) {
               y = 0;
            }

            Graphics var45;
            (var45 = f.aj).setColor(0);
            var45.fillRect(0, 0, 240, 400);
            var51 = GloftMMN.k(0 + y);
            M.a(var51);
            K = a.z;
            M.a(var45, var51, 120, 0, 17, 0);
         }

         var2 = false;
         if (L < 1500 && 400 - i + K / 2 <= 200) {
            L += 83;
            i = 400 + K / 2 - 200;
            var2 = true;
         }

         u[3].a((Graphics)aB, 31, 0, 223, 398, 0, 0, 0);
         aB.setClip(0, 0, 240, 382);
         aB.drawImage(f.m, 0, 400 - i, 20);
         if (!var2) {
            i += 5;
         }

         if (aC[13] < 0) {
            i = -1;
            y = -1;
            L = -1;
            a(6);
            GloftMMN.a("/3", 1);
            GloftMMN.f();
            f.m = null;
            f.aj = null;
         }

         aB.setClip(0, 0, 240, 400);
         a(aB, -1, 0);
         return;
      case 22:
         short[] var49 = new short[]{935, 936};
         aB.setColor(0);
         aB.fillRect(0, 0, 240, 400);
         if (i == -1) {
            i = 0;
            GloftMMN.a("/3", 0);
            GloftMMN.f();
         }

         M.a(aB, g.b(GloftMMN.k(var49[i]), 192), 120, 200, 3, 0);
         var2 = false;
         a(aB, -1, 794);
         a(aB, 901, -1);
         if (aC[14] < 0) {
            ++i;
            i %= var49.length;
         }

         if (aC[13] < 0) {
            i = -1;
            a(6);
            GloftMMN.a("/3", 1);
            GloftMMN.f();
            return;
         }
         break;
      case 24:
         switch(y) {
         case 69:
            GloftMMN.b();
            y = 71;
            i = -1;
            k = true;
            System.out.println("load igp..........");
            c.a(GloftMMN.k(3), ay);
            c.b(0, 0);
            ah = -1;
            break;
         case 70:
            System.gc();
            o[0] = null;
            aB.setColor(0);
            aB.fillRect(0, 0, 240, 400);
            GloftMMN.b("/0");
            Q = Image.createImage(var46 = GloftMMN.j(1), 0, var46.length);
            a(6);
            GloftMMN.c(5);
            i = -1;
            break;
         case 71:
            if (ah < 10) {
               c.b(0, 0);
               ++ah;
            }

            aB.setClip(0, 0, 240, 400);
            c.a(aB);
            byte var44 = 0;
            if (aC[1] < 0) {
               var44 = 21;
            }

            if (aC[2] < 0) {
               var44 = 32;
            }

            if (aC[3] < 0) {
               var44 = 23;
            }

            if (aC[4] < 0) {
               var44 = 24;
            } else if (!aP && aC[14] >= 0) {
               if (aC[13] < 0) {
                  var44 = 26;
               }
            } else {
               var44 = 25;
            }

            if (c.a(var44)) {
               y = 70;
            }
         }

         return;
      case 100:
         aB.setClip(0, 0, 240, 400);
         aB.setColor(0);
         aB.fillRect(0, 0, 240, 400);
         if (bH == null) {
            try {
               bH = Image.createImage("/operatorLogo.png");
            } catch (Exception var42) {
               bI = true;
            }
         } else {
            aB.drawImage(bH, 120, 200, 3);
         }

         long var1 = System.currentTimeMillis();
         if (bL == 0L) {
            bL = var1;
         }

         long var3;
         if ((var3 = var1 - bL) < 0L || var3 > 200L) {
            var3 = 0L;
         }

         bL = var1;
         bK += var3;
         if (bK > bJ) {
            bI = true;
         }

         if (bI) {
            a(4);
            return;
         }
      }

   }

   private static void b(int var0) {
      if (ax == 1) {
         try {
            Display.getDisplay(GloftMMN.b).vibrate(var0);
            return;
         } catch (Exception var2) {
         }
      }

   }

   h() {
      a(0);
      this.setFullScreenMode(true);
      aC = new byte[15];
      aD = new byte[15];
      aK = new int[96000];
      aL = new int[96000];
      aM = Image.createImage(240, 400);
      aN = aM.getGraphics();
   }

   public final void hideNotify() {
      if (af != null) {
         af = af.replace('?', '-');
      }

      aE = true;
      r();
   }

   public final void showNotify() {
      k = true;
      aE = false;
      l = true;
      bT = System.currentTimeMillis();
   }

   static void a() {
      h = -1;
   }

   public final void run() {
      c.a(GloftMMN.b, this, 240, 400);
      System.out.println("igp IS AVAILABLE ::>:: " + c.a());
      System.out.println("paint...2 :: " + b.a);
      System.out.println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
      GloftMMN.f = true;

      for(this.bS = true; h >= 0; System.gc()) {
         long var1 = System.currentTimeMillis();
         this.bR = System.currentTimeMillis();
         if (!aE) {
            this.repaint();
            this.serviceRepaints();
         }

         long var3;
         if ((var3 = System.currentTimeMillis() - this.bR) < 1000L && var3 >= 0L) {
            while(System.currentTimeMillis() - this.bR < 66L) {
               try {
                  System.gc();
                  Thread.sleep(10L);
               } catch (Exception var6) {
               }
            }
         }

         if (System.currentTimeMillis() - var1 > 1500L) {
            this.hideNotify();
            this.showNotify();
         }
      }

      GloftMMN.e();
      GloftMMN.d();
      GloftMMN.c();
      GloftMMN.b.destroyApp(true);
   }

   public final void paint(Graphics var1) {
      try {
         Graphics var2 = var1;
         var1 = aN;
         this.b(var1);
         a(var2, aM);
      } catch (Exception var3) {
         aB.setColor(255, 0, 0);
         aB.fillRect(0, 0, 240, 100);
         aB.setColor(255, 255, 255);
         aB.drawString("" + var3.toString(), 0, 0, 0);
      }
   }

   private static void a(Graphics var0, Image var1) {
      var1.getRGB(aK, 0, 240, 0, 0, 240, 400);
      b(var0, 0, 0);
      b(var0, 120, 0);
      b(var0, 0, 200);
      b(var0, 120, 200);
   }

   private static void b(Graphics var0, int var1, int var2) {
      int var3 = 0;

      for(int var5 = var2; var5 < var2 + 200; ++var5) {
         int var4;
         int var6;
         for(var6 = var1; var6 < var1 + 120; ++var6) {
            var4 = aK[var5 * 240 + var6];
            aL[var3++] = var4;
            aL[var3++] = var4;
         }

         for(var6 = var1; var6 < var1 + 120; ++var6) {
            var4 = aK[var5 * 240 + var6];
            aL[var3++] = var4;
            aL[var3++] = var4;
         }
      }

      var0.drawRGB(aL, 0, 240, var1 << 1, var2 << 1, 240, 400, false);
   }

   private void b(Graphics var1) {
      if (this.bS) {
         if (!aE) {
            aF = System.currentTimeMillis();
            aH = aF - bT;
            bT = aF;
            ++aG;

            try {
               aB = var1;
               this.e();
            } catch (Exception var4) {
               var4.printStackTrace();
            }

            this.q();
            GloftMMN.d();
            k = false;
            l = false;
         }
      }
   }

   protected final void keyPressed(int var1) {
      r();
      int var2 = var1;
      int var3;
      if ((var3 = this.getGameAction(var1)) != 0) {
         switch(var3) {
         case 1:
            var2 = 50;
            break;
         case 2:
            var2 = 52;
         case 3:
         case 4:
         case 7:
         default:
            break;
         case 5:
            var2 = 54;
            break;
         case 6:
            var2 = 56;
            break;
         case 8:
            var2 = 53;
         }
      }

      byte var4 = c(var2);
      if (aD[var4] <= 0) {
         if (aD[var4] < 0) {
            aD[var4] = 0;
         }

         if (aD[var4] < 126) {
            ++aD[var4];
         }

      }
   }

   protected final void keyReleased(int var1) {
      int var2 = var1;
      int var3;
      if ((var3 = this.getGameAction(var1)) != 0) {
         switch(var3) {
         case 1:
            var2 = 50;
            break;
         case 2:
            var2 = 52;
         case 3:
         case 4:
         case 7:
         default:
            break;
         case 5:
            var2 = 54;
            break;
         case 6:
            var2 = 56;
            break;
         case 8:
            var2 = 53;
         }
      }

      byte var4 = c(var2);
      if (aD[var4] > 0) {
         if (aD[var4] == 1) {
            aO = var4;
         }

         byte[] var10000 = aD;
         var10000[var4] *= -1;
      }

   }

   private static byte c(int var0) {
      aQ = false;
      if (var0 != -6 && var0 != -21 && var0 != 21) {
         if (var0 != -7 && var0 != -22 && var0 != 22) {
            if (var0 == 53) {
               return 5;
            } else if ((var0 = Math.abs(var0)) == 1) {
               aQ = true;
               return 1;
            } else if (var0 == 3) {
               aQ = true;
               return 3;
            } else if (var0 == 4) {
               aQ = true;
               return 4;
            } else if (var0 == 2) {
               aQ = true;
               return 2;
            } else if (var0 == 5) {
               aQ = true;
               return 5;
            } else if (var0 == 35) {
               return 12;
            } else if (var0 == 42) {
               return 11;
            } else if (var0 == 48) {
               return 6;
            } else if (var0 == 49) {
               return 7;
            } else if (var0 == 50) {
               return 1;
            } else if (var0 == 51) {
               return 8;
            } else if (var0 == 52) {
               return 3;
            } else if (var0 == 54) {
               return 4;
            } else if (var0 == 55) {
               return 9;
            } else if (var0 == 56) {
               return 2;
            } else {
               return (byte)(var0 == 57 ? 10 : 0);
            }
         } else {
            return 13;
         }
      } else {
         return 14;
      }
   }

   private static void a(g var0, d var1, boolean var2, boolean var3, boolean var4, boolean var5) {
      System.out.println("UpadateMovement");
      if (var4 && var3) {
         var0.a(0, -721, 6, 0, false);
      } else if (var4 && var2) {
         var0.a(-721, 0, 6, 1, false);
      } else if (var5 && var3) {
         var0.a(721, 0, 4, 0, false);
      } else if (var5 && var2) {
         var0.a(0, 721, 4, 1, false);
      } else if (var3) {
         var0.a(512, -512, 4, 0, false);
      } else if (var2) {
         var0.a(-512, 512, 4, 1, false);
      } else if (var4) {
         var0.a(-768, -768, 7, var1.c, false);
      } else {
         if (var5) {
            var0.a(768, 768, 5, var1.c, false);
         }

      }
   }

   private static boolean a(int var0, int var1, int var2, int var3) {
      return b(var0, var1, var2, var3);
   }

   private static boolean b(int var0, int var1, int var2, int var3) {
      return a(var0, var1, var2, var3, true);
   }

   private static boolean a(int var0, int var1, int var2, int var3, boolean var4) {
      if (!by) {
         return false;
      } else if (bB >= var0 && bB <= var0 + var2 && bC >= var1 && bC <= var1 + var3) {
         if (var4) {
            bB = -1;
            bC = -1;
         }

         return true;
      } else {
         return false;
      }
   }

   protected final synchronized void pointerPressed(int var1, int var2) {
      var1 >>= 1;
      var2 >>= 1;
      if (var1 <= 60 && var2 >= 360) {
         aW = true;
      }

      if (var1 >= 180 && var2 >= 360) {
         aX = true;
      }

      if (aV < 1024) {
         aS[aV] = 1;
         aT[aV] = var1;
         aU[aV++] = var2;
      }

      bz = var1;
      bA = var2;
      bD = -1;
      bE = -1;
      bB = -1;
      bC = -1;
      by = true;
      if (h == 24) {
         if (bz > 200 && bA > 170 && bA < 230) {
            bz = 225;
            bA = 200;
         }

         c.b(bz, bA);
      }

   }

   protected final synchronized void pointerReleased(int var1, int var2) {
      var1 >>= 1;
      var2 >>= 1;
      aW = false;
      aX = false;
      boolean var3 = a(var1, var2, 0, 358, 50, 42);
      boolean var4 = a(var1, var2, 190, 358, 80, 52);
      if (var3 && (y != 67 || i != 0)) {
         this.keyPressed(-6);
         this.keyReleased(-6);
      }

      if (var4) {
         this.keyPressed(-7);
         this.keyReleased(-7);
      }

      if (aV < 1024) {
         aS[aV] = 2;
         aT[aV] = var1;
         aU[aV++] = var2;
      }

      bB = var1;
      bC = var2;
      bz = -1;
      bA = -1;
      bD = -1;
      bE = -1;
      if (h == 24) {
         if (bB > 200 && bC > 170 && bC < 230) {
            bB = 225;
            bC = 200;
         }

         c.a(bB, bC);
      }

   }

   protected final synchronized void pointerDragged(int var1, int var2) {
      var1 >>= 1;
      var2 >>= 1;
      if (aV < 1024) {
         aS[aV] = 3;
         aT[aV] = var1;
         aU[aV++] = var2;
      }

      bD = var1;
      bE = var2;
      bz = -1;
      bA = -1;
      bB = -1;
      bC = -1;
      if (h == 24) {
         if (bD > 200 && bE > 170 && bE < 230) {
            bD = 225;
            bE = 200;
         }

         c.c(bD, bE);
      }

   }

   private synchronized void f() {
      int var1 = aV;

      for(int var2 = 0; var2 < var1; ++var2) {
         if (aS[var2] != 3 || var2 >= var1 - 1 || aS[var2 + 1] != 3) {
            this.a(aS[var2], aT[var2], aU[var2]);
         }
      }

      aV = 0;
   }

   private void g() {
      if (h == 8 && y == 0 && bG) {
         a(aB, 796, -1);
      }

      if (y == 67 && i == -1) {
         a(aB, 849, -1);
      }

      if (y == 24 || y == 26 || y == 27) {
         a(aB, -1, 0);
      }

      if (h == 18 && y != 38 && y != 45 && y != 68 && y != 55 && y != 50 || y == 3 || y == 4 || y == 34 || y == 16) {
         a(aB, -1, 0);
      }

      if (h == 15) {
         a(aB, 401, 0);
         if (y == 31 && (i == -3 || i == -8) && a(0, 75, 240, 240, true)) {
            this.keyPressed(53);
            this.keyReleased(53);
            return;
         }
      }

      if (h == 17 && g.ak == null) {
         a(aB, 848, -1);
         if (i != 0 && a(2, 114, 236, 16, true) && i != 63) {
            i = 1;
            this.keyPressed(50);
            return;
         }

         if (a(2, 133, 236, 18, true) && i != 63) {
            if (i == 1) {
               this.keyPressed(53);
               this.keyReleased(53);
               return;
            }

            i = 2;
            this.keyPressed(50);
            return;
         }

         if (a(2, 160, 236, 20, true)) {
            if (i == 2) {
               this.keyPressed(53);
               this.keyReleased(53);
               return;
            }

            i = 3;
            this.keyPressed(50);
            return;
         }

         if (i != 3 && a(1, 189, 81, 34, true)) {
            i = 4;
            this.keyPressed(50);
            return;
         }

         if (i != 4 && a(1, 224, 81, 19, true)) {
            i = 5;
            this.keyPressed(50);
            return;
         }

         if (i != 5 && a(1, 244, 81, 27, true)) {
            i = 6;
            this.keyPressed(50);
            return;
         }

         if (i != 6 && a(160, 189, 76, 34, true)) {
            i = 7;
            this.keyPressed(50);
            return;
         }

         if (i != 7 && a(160, 224, 76, 19, true)) {
            i = 8;
            this.keyPressed(50);
            return;
         }

         if (i != 8 && a(160, 244, 76, 27, true)) {
            i = 9;
            this.keyPressed(50);
            return;
         }

         if (i != 9 && a(1, 278, 235, 36, true)) {
            i = 10;
            this.keyPressed(50);
            return;
         }
      }

      if (h == 8) {
         if (y != 32 && y != 33 && y != 35 && y < 73 && y != 3 && y != 4 && y != 5 && y != 8) {
            b();
            if (a(57, 315, 126, 35, true)) {
               System.out.println("1212121212121212121211212");
               if (f.aY > 0 && f.au >= 0) {
                  f.b(aB, 3);
                  return;
               }

               this.keyPressed(53);
               this.keyReleased(53);
               return;
            }
         }

         if (y == 67 && i >= 0) {
            for(int var1 = 0; var1 < 6; ++var1) {
               if (a(bD, bE, 1, 146 + var1 * 30, 238, 30)) {
                  i = var1;
               }

               if (a(bz, bA, 1, 146 + var1 * 30, 238, 30)) {
                  i = var1;
               }

               if (a(bB, bC, 1, 146 + var1 * 30, 238, 30)) {
                  i = var1;
               }

               if (a(1, 146 + var1 * 30, 238, 30, true)) {
                  this.keyPressed(-6);
                  this.keyReleased(-6);
                  return;
               }
            }
         }

         if ((y == 17 || y == 13) && a(0, 75, 240, 240, true)) {
            this.keyPressed(53);
            this.keyReleased(53);
            return;
         }

         if (y == 8 && a(0, 0, 240, 400, true)) {
            this.keyPressed(53);
            this.keyReleased(53);
            return;
         }

         if (y == 0) {
            if (bc && bh == -1) {
               if (a(bb[0] - 5, bb[1] - 25, 20, 28, true)) {
                  if (f.aY > 0 && f.au >= 0) {
                     f.b(aB, 3);
                     return;
                  }

                  this.keyPressed(53);
                  this.keyReleased(53);
                  return;
               }

               bc = false;
            }

            if (be && bh == -1) {
               if (a(bd[0] - 5, bd[1] - 25, 20, 28, true)) {
                  System.out.println("545454545454545454545454");
                  this.keyPressed(53);
                  this.keyReleased(53);
                  return;
               }

               be = false;
            }

            if (bg && bh == -1) {
               if (a(bf[0], bf[1], 37, 37, true)) {
                  System.out.println("dfgdfgdfgdfgfdgdfgfdgdfgfd");
                  this.keyPressed(53);
                  this.keyReleased(53);
                  return;
               }

               if (a(bf[2], bf[3], 197, 18, true)) {
                  this.keyPressed(53);
                  this.keyReleased(53);
                  return;
               }

               bg = false;
            }

            if (a(0, 75, 240, 240, false)) {
               bi[0] = bB;
               bi[1] = bC;

               try {
                  this.n();
               } catch (Exception var3) {
                  System.out.println("Here error..........");

                  int var2;
                  for(var2 = 0; var2 < 4; ++var2) {
                     this.keyReleased(bj[var2]);
                  }

                  for(var2 = 0; var2 < 4; ++var2) {
                     this.keyReleased(bk[var2]);
                  }
               }
            }
         }

         a(0, 0, 240, 400, true);
      }

   }

   private void a(int var1, int var2, int var3) {
      int var4;
      int var5;
      int var6;
      int var7;
      label59:
      switch(var1) {
      case 1:
         var4 = 0;

         while(true) {
            if (var4 >= ba) {
               break label59;
            }

            var5 = var4 * 7;
            if (a(var2, var3, aZ, var5)) {
               var6 = aZ[var5 + 4] & 255;
               var7 = aZ[var5 + 4] >> 8;
               this.b(var6, var7);
               break label59;
            }

            ++var4;
         }
      case 2:
         var4 = 0;

         while(true) {
            if (var4 >= ba) {
               break label59;
            }

            var5 = var4 * 7;
            if (a(var2, var3, aZ, var5)) {
               var6 = aZ[var5 + 5] & 255;
               var7 = aZ[var5 + 5] >> 8;
               this.b(var6, var7);
               break label59;
            }

            ++var4;
         }
      case 3:
         for(var4 = 0; var4 < ba; ++var4) {
            var5 = var4 * 7;
            if (a(var2, var3, aZ, var5)) {
               var6 = aZ[var5 + 6] & 255;
               var7 = aZ[var5 + 6] >> 8;
               this.b(var6, var7);
               break;
            }
         }
      }

      for(var4 = 0; var4 < ba; ++var4) {
         if (a(var2, var3, aZ, var4 * 7)) {
            if (U != null && U.length > 0) {
               U[0] = aZ[var4 * 7 + 6] >> 8;
               return;
            }
            break;
         }
      }

   }

   private static boolean a(int var0, int var1, int var2, int var3, int var4, int var5) {
      return var0 >= var2 && var0 <= var2 + var4 && var1 >= var3 && var1 <= var3 + var5;
   }

   private static boolean a(int var0, int var1, int[] var2, int var3) {
      return var0 >= var2[var3] && var0 <= var2[var3] + var2[var3 + 2] && var1 >= var2[var3 + 1] && var1 <= var2[var3 + 1] + var2[var3 + 3];
   }

   public static void b() {
      ba = 0;
      if (aY) {
         System.out.println("------------------");
         System.out.println("StylusInit");
      }

   }

   private static void b(int var0, int var1, int var2, int var3, int var4, int var5) {
      a(var0, var1, var2, var3, 0, 0, var4, var5, 0, 0, false);
   }

   private static void a(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      a(var0, var1, var2, var3, var4, var5, var6, var7, 0, 0, false);
   }

   public static void a(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9) {
      a(var0, var1, var2, var3, var4, var5, var6, var7, var8, var9, false);
   }

   private static void a(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, boolean var10) {
      if (ba < 50) {
         if (var10) {
            System.arraycopy(aZ, 0, aZ, 7, ba * 7);
         }

         int var11 = (var10 ? 0 : ba) * 7;
         aZ[var11++] = var0;
         aZ[var11++] = var1;
         aZ[var11++] = var2;
         aZ[var11++] = var3;
         aZ[var11++] = var4 & 255 | var5 << 8;
         aZ[var11++] = var6 & 255 | var7 << 8;
         aZ[var11] = var8 & 255 | var9 << 8;
         ++ba;
      }
   }

   private static int[] a(d var0) {
      int[] var1;
      a(((var1 = var0.c())[0] >> 8) - 20, (var1[1] >> 8) - 15, 53, (var1[3] - var1[1] >> 8) + 30, 1, 52, 2, 52);
      a((var1[2] >> 8) - 13, (var1[1] >> 8) - 15, 53, (var1[3] - var1[1] >> 8) + 30, 1, 54, 2, 54);
      return var1;
   }

   private void h() {
      this.keyPressed(53);
      this.keyReleased(53);
   }

   private void i() {
      this.keyPressed(50);
      this.keyReleased(50);
   }

   private void j() {
      this.keyPressed(56);
      this.keyReleased(56);
   }

   private void k() {
      this.keyPressed(52);
      this.keyReleased(52);
   }

   private void l() {
      this.keyPressed(54);
      this.keyReleased(54);
   }

   private void m() {
      this.keyPressed(-7);
      this.keyReleased(-7);
   }

   private void d(int var1) {
      this.keyPressed(var1);
   }

   private void e(int var1) {
      this.keyReleased(var1);
   }

   private void b(int var1, int var2) {
      switch(var1) {
      case 1:
         this.d(var2);
         return;
      case 2:
         this.e(var2);
         return;
      case 3:
         this.h();
         return;
      case 4:
         this.i();
         return;
      case 5:
         this.j();
         return;
      case 6:
         this.k();
         return;
      case 7:
         this.l();
         return;
      case 8:
         U[0] = var2;
         return;
      case 9:
         U[0] = var2;
         this.h();
         return;
      case 10:
         if (var2 == 0) {
            this.k();
         } else {
            this.l();
         }

         this.h();
         return;
      case 11:
         return;
      case 12:
         if ((var2 & 1048576) != 0) {
            this.l();
         }

         if ((var2 & 2097152) != 0) {
            this.k();
            return;
         }
         break;
      case 13:
         if ((var2 & 1048576) != 0) {
            this.l();
         }

         if ((var2 & 2097152) != 0) {
            this.k();
            return;
         }
         break;
      case 14:
         if ((var2 & 1048576) != 0) {
            this.l();
         }

         if ((var2 & 2097152) != 0) {
            this.k();
            return;
         }
         break;
      case 15:
         return;
      case 16:
         this.h();
         return;
      case 17:
         if (var2 > 0) {
            this.j();
            return;
         }

         if (var2 <= 0) {
            this.i();
            return;
         }
         break;
      case 18:
         return;
      case 19:
         this.h();
         return;
      case 20:
         return;
      case 21:
         return;
      case 22:
         bF[2] = var2;
         bF[4] = bF[1];
         return;
      case 23:
         this.m();
         return;
      case 24:
         return;
      case 25:
         if (aY) {
            System.out.println("Minimap Location: " + var2);
            return;
         }
         break;
      case 26:
         i = var2;
         this.h();
         return;
      case 27:
      case 28:
      default:
         break;
      case 29:
         B = (byte)var2;
      }

   }

   private static int f(int var0) {
      return (var0 << 8) / 3;
   }

   private static int g(int var0) {
      return (var0 + 42) * 3 >> 8;
   }

   static void c() {
      bs = f.t * 3;
      bt = f.u * 3;
      bm = new byte[bs * bt];

      for(int var0 = 0; var0 < bm.length; ++var0) {
         bm[var0] = (byte)(f.a(f(var0 % bs), f(var0 / bs), 127) ? 2 : 0);
      }

   }

   static void d() {
      bo = null;
      br = null;
      bp = 1;
      bq = 0;
      bn = null;
      bm = null;
   }

   private void n() {
      bi[0] += f.C;
      bi[1] = bi[1] - 75 + f.D;

      try {
         f.b(bi[0], bi[1], bi);
      } catch (Exception var3) {
      }

      bi[0] = g(bi[0]);
      bi[1] = g(bi[1]);
      if (bi[0] >= 0 && bi[1] >= 0) {
         try {
            o();
         } catch (Exception var2) {
         }

         b(true);
         b(false);
      }
   }

   private static void o() {
      bo = new int[bs * bt];
      br = new int[bs * bt];
      bn = new byte[bs * bt];
      System.arraycopy(bm, 0, bn, 0, bs * bt);

      for(int var0 = 1; var0 < s.length; ++var0) {
         g var1;
         if ((var1 = s[var0]) != null) {
            for(int var4 = 0; var4 < bu.length; ++var4) {
               int var2 = g(var1.c[0] + bu[var4]);
               ++var4;
               int var3 = g(var1.c[1] + bu[var4]);
               if (var2 <= 0 || var3 <= 0 || var2 > bs || var3 > bt) {
                  break;
               }

               bn[var2 + var3 * bs] = 1;
            }
         }
      }

   }

   private static void p() {
      bl = g(s[0].c[1]) * bs + g(s[0].c[0]);
   }

   private static void c(int var0, int var1) {
      br[var0] = var1;
      bo[bq++] = var0;
      bn[var0] = 3;
   }

   private static void b(boolean var0) {
      try {
         bq = 0;
         bp = 0;
         p();
         br[bl] = -1;
         int var1 = bi[0] + bi[1] * bs;
         if (var0) {
            if (bn[var1] == 0) {
               return;
            }

            bo[bq++] = var1;
         } else {
            if (var1 == bl) {
               bp = 1;
               return;
            }

            bo[bq++] = bl;
         }

         while(bq > bp) {
            int var2 = bo[bp++];
            bn[var2] = 3;

            for(int var3 = 0; var3 < bv.length; ++var3) {
               int var4 = var2 % bs + bv[var3];
               ++var3;
               int var5 = var2 / bs + bv[var3];
               if (var4 > 0 && var5 > 0 && var4 < bs && var5 < bt) {
                  int var6 = var5 * bs + var4;
                  if (var0) {
                     if (bn[var6] == 0) {
                        bi[0] = var4;
                        bi[1] = var5;
                        return;
                     }

                     c(var6, var2);
                  } else {
                     if (bi[0] == var4 && bi[1] == var5) {
                        br[var6] = var2;
                        bq = 0;
                        bp = 0;

                        int var7;
                        for(var7 = var6; var7 >= 0; var7 = br[var7]) {
                           bo[bq++] = var7;
                        }

                        --bq;
                        if (bq > 0) {
                           var7 = f(bo[bq - 1] % bs);
                           int var8 = f(bo[bq - 1] / bs);
                           int var9 = f(bo[bq] % bs) - var7;
                           int var10 = f(bo[bq] / bs) - var8;
                           var7 = s[0].c[0] - var7;
                           var8 = s[0].c[1] - var8;
                           if (var9 * var9 + var10 * var10 >= var7 * var7 + var8 * var8) {
                              --bq;
                           }
                        }

                        return;
                     }

                     if (bn[var6] == 0) {
                        c(var6, var2);
                     }
                  }
               }
            }
         }
      } catch (Exception var11) {
         var11.printStackTrace();
      }

      bq = 0;
      System.out.println("path not found" + var0);
   }

   private static boolean a(long var0, long var2) {
      return ((var2 - var0) * (var2 - var0) << 8) / 1048576L + ((var0 + var2) * (var0 + var2) << 8) / 2359296L <= 256L;
   }

   private static boolean b(long var0, long var2) {
      return ((var2 - var0) * (var2 - var0) << 8) / 1048576L + ((var0 + var2) * (var0 + var2) << 8) / 2359296L <= 236L;
   }

   private static void c(int var0, int var1, int var2, int var3) {
      int var4 = 0;

      while(var4 < 10) {
         ++var4;
         bw = (var0 + var2) / 2;
         bx = (var1 + var3) / 2;
         if (b((long)bw, (long)bx)) {
            var0 = bw;
            var1 = bx;
         } else {
            if (a((long)bw, (long)bx)) {
               break;
            }

            var2 = bw;
            var3 = bx;
         }
      }

      if (var4 >= 10) {
         bw = var0;
         bx = var1;
      }

   }

   private void q() {
      this.f();
      this.g();

      for(int var1 = 0; var1 < 15; ++var1) {
         aC[var1] = aD[var1];
         if (var1 == 5) {
            aP = false;
            if (aD[var1] == 1 || aD[var1] == -1) {
               aP = true;
            }
         }

         if (aD[var1] != 0) {
            if (aD[var1] < 0) {
               aD[var1] = 0;
            } else if (aD[var1] < 126) {
               ++aD[var1];
               if (var1 >= 13) {
                  byte[] var10000 = aD;
                  var10000[var1] *= -1;
               }
            }
         }
      }

   }

   private static void r() {
      for(int var0 = 0; var0 < 15; ++var0) {
         aC[var0] = 0;
         aD[var0] = 0;
      }

   }

   private static void c(Graphics var0, int var1, int var2) {
      v[17].d.a((Graphics)var0, 86, 0, var1, var2, 0, 0, 0);
      d var3;
      (var3 = n[0]).a = var1 << 8;
      var3.b = var2 << 8;
      var3.d.m = s[0].b[15];
      var3.a(var0);
      var3.b();
      if (var3.a()) {
         ++c[0];
         byte[] var10000 = c;
         var10000[0] = (byte)(var10000[0] % (c.length - 1));
         var3.a(c[c[0] + 1]);
      }

   }

   static void a(Graphics var0, int var1, int var2, int var3, int var4, int var5) {
      int var7 = -1;
      int var8 = -1;
      String var9 = null;
      int var10;
      if (h == 18) {
         if (bF[0] == -1) {
            var10 = v[13].e;
         } else {
            var10 = v[14].e;
         }
      } else {
         var10 = v[7].e;
      }

      boolean var11 = false;
      byte var12 = 0;
      byte var26;
      if (var2 == 255) {
         var26 = 17;
      } else if (var3 == 255) {
         var26 = 24;
      } else {
         var26 = 20;
      }

      var0.setClip(0, 0, 240, 400);
      if (var10 != 24) {
         d var13;
         if (var10 == 25) {
            var13 = v[5];
            if (var2 == 255) {
               var26 = 17;
            } else if (var3 == 255) {
               var26 = 20;
            } else {
               var26 = 24;
            }

            var4 = 240 - var4;
            boolean var14 = h == 17 && i == 63;
            bG = z == 0 || y == 0 || z == 21;
            if (y != 25) {
               switch(var1) {
               case 1:
                  if (var14) {
                     var8 = 909;
                  } else if ((h == 16 || h == 15) && y != 77) {
                     var8 = 0;
                  }
                  break;
               case 2:
                  if (h != 16 && !var14 && h != 15) {
                     if (h != 17 && y != 67) {
                        v[10].a(3);
                        v[10].a = var4 << 8;
                        v[10].b = var5 << 8;
                        v[10].a(var0);
                        if (h != 18 && y != 3 && y != 4 && y != 34 && y != 16) {
                           var13.e = -1;
                           var13.a(0);
                           var13.a = var4 << 8;
                           var13.b = var5 << 8;
                           var13.a(var0);
                        }
                     }
                  } else if (y != 77) {
                  }
               case 3:
               default:
                  break;
               case 4:
                  if (h == 15) {
                     var8 = 401;
                  } else if (h == 17 && i == 63) {
                     var8 = 849;
                  } else if (h == 17) {
                     var8 = 848;
                  } else if (y == 27 && f.bc[31] == 0) {
                     if ((g.ah[14] == 1 || g.ah[7] == 1 || g.ah[28] == 1) && !as) {
                        var8 = 875;
                     }
                  } else if (y == 77) {
                     var8 = 849;
                  } else if (y == 67) {
                     var8 = 849;
                  } else if (h != 16 && bG) {
                     var8 = 796;
                  }
               }
            }
         } else if (var10 == 27 && var1 == 1) {
            var7 = 39;
         } else if (var10 == 29 && var1 == 2) {
            var7 = 40;
         } else if (var10 == 26 && var1 == 2) {
            var7 = g.ao & 255;
         } else if (var10 == 47) {
            switch(var1) {
            case 2:
               var0.drawImage(Q, 0, 0, 20);
               break;
            case 3:
               if (h != 7 && !f.ah && (var13 = n[0]).d.w != null && var13.d.w[0] != null) {
                  v[17].d.a((Graphics)var0, 86, 0, var4, var5, 0, 0, 0);
                  var13.a(0);
                  var13.a = var4 << 8;
                  var13.b = var5 << 8;
                  var13.d.m = s[0].b[15];
                  var13.a(var0);
               }
               break;
            case 4:
               if (g.ad != null) {
                  if ((g.ae & 256) == 256 && X == 0) {
                     var9 = g.b(GloftMMN.k(914), 215);
                  } else {
                     var9 = g.b(g.ad, 215);
                  }

                  var26 = 17;
                  var4 = 120;
               }
            case 5:
            }
         } else if (var10 == 48) {
            if (var1 == 5) {
               var12 = 1;
               var9 = g.b(GloftMMN.k(820), 229);
            }
         } else if (var10 == 49) {
            if (var1 > 0) {
               if (s[0].a[0] == 0) {
                  var8 = 822 + (var1 - 1);
               } else {
                  var8 = 926 + (var1 - 1);
               }
            } else {
               var8 = 821;
            }
         } else if (var10 == 50) {
            if (var1 == 1) {
               c(var0, var4, var5 - 21);
            }
         } else if (var10 == 51) {
            if (var1 == 2) {
               var9 = g.b(GloftMMN.k(828), 229);
            }
         } else {
            g var6;
            int var15;
            int var21;
            int var28;
            d var31;
            byte var35;
            int var36;
            label761:
            switch(h) {
            case 15:
               var12 = 0;
               switch(var1) {
               case 1:
                  var8 = 863;
                  break label761;
               case 2:
                  var35 = 1;
                  if (f.l()) {
                     var35 = 0;
                  } else if (f.j()) {
                     var35 = 3;
                  } else if (f.k()) {
                     var35 = 2;
                  }

                  u[5].a((Graphics)var0, var35, 0, var4, var5, 0, 0, 0);
               case 10:
                  break label761;
               default:
                  byte var27 = 22;
                  var36 = 0;
                  int var42 = 0;

                  for(int var43 = 0; var43 < 39; ++var43) {
                     if (g.h[var43] == var1 - 3 && g.ah[var43] == 1 && g.f[var43] == 1) {
                        ++var42;
                     }
                  }

                  if (i == var1 - 3) {
                     var27 = 23;
                  } else if (g.aa == var1 - 3) {
                     var36 = K % 12 < 6 ? 0 : 1;
                     var27 = 24;
                  } else if (var42 == 0) {
                     var27 = 96;
                  }

                  int[] var44;
                  if (var27 == 96) {
                     u[1].a((Graphics)var0, 96, 0, var4, var5, 0, 0, 0);
                     var44 = new int[]{0, 0, 0, 0};
                     u[1].a((int[])var44, var27, 0, var4 << 8, var5 << 8, 0, 0, 0);
                     b((var44[0] >> 8) - 10, (var44[1] >> 8) - 10, (var44[2] - var44[0] >> 8) + 20, (var44[3] - var44[1] >> 8) + 20, 26, var1 - 3);
                  } else {
                     u[0].a((Graphics)var0, var27, var36, var4, var5, 0, 0, 0);
                     var44 = new int[]{0, 0, 0, 0};
                     u[0].a((int[])var44, var27, 0, var4 << 8, var5 << 8, 0, 0, 0);
                     b((var44[0] >> 8) - 10, (var44[1] >> 8) - 10, (var44[2] - var44[0] >> 8) + 20, (var44[3] - var44[1] >> 8) + 20, 26, var1 - 3);
                  }

                  if (var27 == 24) {
                     u[1].a((Graphics)var0, 88, var36, var4, var5, 0, 0, 0);
                     var44 = new int[]{0, 0, 0, 0};
                     u[1].a((int[])var44, var27, 0, var4 << 8, var5 << 8, 0, 0, 0);
                     b((var44[0] >> 8) - 10, (var44[1] >> 8) - 10, (var44[2] - var44[0] >> 8) + 20, (var44[3] - var44[1] >> 8) + 20, 26, var1 - 3);
                  }

                  if (var27 == 23) {
                     var44 = new int[]{0, 0, 0, 0};
                     u[0].a((int[])var44, var27, 0, var4 << 8, var5 << 8, 0, 0, 0);
                     d var25;
                     (var25 = v[2]).a = (var44[0] + var44[2]) / 2;
                     var25.b = var44[1];
                     var25.a(var0);
                     var25.b();
                     if (var25.e == 2 && var25.a()) {
                        var25.a(3);
                     }

                     b(((var44 = var25.c())[0] >> 8) - 10, (var44[1] >> 8) - 10, (var44[2] - var44[0] >> 8) + 20, (var44[3] - var44[1] >> 8) + 20, 3, 0);
                  }

                  if (var1 - 3 == g.h[H] && aG % 12L < 8L) {
                     u[0].a((Graphics)var0, 26, 0, var4, var5, 0, 0, 0);
                  }

                  if (f.bc[0] >= 30 && var1 - 3 == 2 || f.bc[0] < 30 && var1 - 3 == 0) {
                     u[0].a((Graphics)var0, 25, 0, var4, var5, 0, 0, 0);
                  }

                  if (f.bc[25] != -1) {
                     byte var46 = f.bc[24];
                     byte var45 = g.h[var46];
                     if (var1 - 3 == var45) {
                        u[1].a((Graphics)var0, 94, 0, var4, var5, 0, 0, 0);
                     }
                  }
                  break label761;
               }
            case 16:
               g var34 = s[0];
               Image var40;
               switch(y) {
               case 24:
                  if (var10 == 13) {
                     switch(var1) {
                     case 12:
                        var8 = 860;
                        break label761;
                     case 13:
                        var12 = 1;
                        var9 = GloftMMN.k(861) + " " + GloftMMN.k(s[4].a[1]);
                        break label761;
                     case 14:
                        var12 = 1;
                        g.b(g.ad, 240 - var4 * 2);
                        var26 = 17;
                        var4 = 112;
                        var9 = g.b(g.ad, 212);
                        break label761;
                     case 15:
                        var40 = g.U[3];
                        var0.drawImage(var40, var4 - var40.getWidth() / 2, var5 - var40.getHeight() / 2, 20);
                     case 16:
                     }
                  }
                  break label761;
               case 25:
                  if (var10 == 15) {
                     var6 = s[1 + bF[0]];
                     var12 = 1;
                     switch(var1) {
                     case 14:
                        var12 = 0;
                        var8 = 856;
                        break label761;
                     case 15:
                        var8 = var6.a[1];
                        break label761;
                     case 16:
                        if (bF[2] + bF[3] + bF[4] == 3) {
                           var8 = 858;
                        } else {
                           var8 = 859;
                        }
                        break label761;
                     case 17:
                        var40 = g.U[bF[0]];
                        var0.drawImage(var40, var4 - var40.getWidth() / 2, var5 - var40.getHeight() / 2, 20);
                        break label761;
                     case 18:
                        var9 = g.b(GloftMMN.k(806 + bF[0] * 2), 240 - var4 * 2);
                     }
                  }
                  break label761;
               case 26:
                  var21 = var1 + bF[0];
                  if (var1 >= 20 && var1 <= 23) {
                     var21 -= 20;
                     var8 = 180 + V[var21];
                  } else if (var1 >= 24 && var1 <= 27) {
                     var21 -= 24;
                     f.a((Graphics)var0, V[var21], var4, var5, 5);
                  } else if (var1 >= 33 && var1 <= 36) {
                     var21 -= 33;
                     var12 = 1;
                     var9 = Integer.toString(var34.a[V[var21]]);
                  } else {
                     switch(var1) {
                     case 19:
                        var9 = af;
                     case 20:
                     case 21:
                     case 22:
                     case 23:
                     case 24:
                     case 25:
                     case 26:
                     case 27:
                     case 33:
                     case 34:
                     case 35:
                     case 36:
                     default:
                        break label761;
                     case 28:
                        var7 = 84 + var34.a[0];
                        break label761;
                     case 29:
                        var7 = 79;
                        break label761;
                     case 30:
                        var7 = 92;
                        break label761;
                     case 31:
                        var7 = 62;
                        break label761;
                     case 32:
                        var7 = 95;
                        break label761;
                     case 37:
                        var12 = 1;
                        var9 = f.e(var34.a[35]);
                        break label761;
                     case 38:
                        var8 = 887;
                        break label761;
                     case 39:
                        var9 = Integer.toString(var34.a[36]);
                        break label761;
                     case 40:
                        var9 = Integer.toString(var34.a[37]);
                        break label761;
                     case 41:
                        var9 = Integer.toString(var34.a[38]);
                        break label761;
                     case 42:
                        n[0].d.m = var34.b[15];
                        n[0].d.a((Graphics)var0, 0, 0, var4, var5, 0, 0, 0);
                     }
                  }
                  break label761;
               case 27:
                  if (var10 == 20) {
                     var12 = 1;
                     switch(var1) {
                     case 8:
                        var12 = 0;
                        var8 = 802;
                        break label761;
                     case 9:
                        var8 = 874;
                        break label761;
                     case 10:
                        var8 = 803;
                     }
                  } else {
                     g var39 = s[g.G[bF[0]]];
                     switch(var1) {
                     case 3:
                        var7 = 84 + var39.a[0];
                        break label761;
                     case 4:
                        if (var10 == 21) {
                           var12 = 1;
                        }

                        var8 = var39.a[1];
                        break label761;
                     case 5:
                        g.a(var0, var39, var4, var5);
                     }
                  }
               default:
                  break label761;
               }
            case 17:
               var6 = s[0];
               var15 = ((var28 = 7 + var6.a[0] * 2) - 7) / 2;
               boolean var29 = false;
               boolean var37 = false;
               if (var10 == 33) {
                  switch(var1) {
                  case 25:
                     var8 = 814;
                  case 26:
                  case 28:
                  case 32:
                  case 34:
                  case 41:
                  default:
                     break label761;
                  case 27:
                     var8 = 180;
                     break label761;
                  case 29:
                     var8 = 181;
                     break label761;
                  case 30:
                     var7 = 84 + var6.a[0];
                     break label761;
                  case 31:
                     var9 = af;
                     break label761;
                  case 33:
                     var8 = 885;
                     break label761;
                  case 35:
                     var9 = "" + (var6.a[4] + 1);
                     break label761;
                  case 36:
                     var9 = "" + (var6.a[6] + 1);
                     break label761;
                  case 37:
                     var9 = "" + (var6.a[8] + 2);
                     break label761;
                  case 38:
                     var0.setColor(p[var15][5].k[var6.a[5]][2]);
                     var0.fillRect(var4, var5, 23, 12);
                     break label761;
                  case 39:
                     var0.setColor(p[var15][2].k[var6.a[7]][5]);
                     var0.fillRect(var4, var5, 23, 12);
                     break label761;
                  case 40:
                     var0.setColor(p[var15][4].k[var6.a[9]][5]);
                     var0.fillRect(var4, var5, 23, 12);
                     break label761;
                  case 42:
                     var8 = 192;
                     break label761;
                  case 43:
                     var0.setColor(W[var6.a[12]]);
                     var0.fillRect(var4, var5, 23, 12);
                  }
               } else if ((var10 != 34 || var1 != 1) && (var10 != 41 || var1 != 2)) {
                  if (i == 0) {
                     var12 = 1;
                     if (var1 == 2) {
                        var8 = 180;
                     } else if (var1 == 3) {
                        var7 = 84 + var6.a[0];
                     }
                  } else if (i != 1 && i != 63) {
                     if (i == 2) {
                        var12 = 1;
                        var8 = 885;
                     } else if (var10 == 44) {
                        if (var1 == 5) {
                           var0.setColor(p[var15][5].k[var6.a[5]][2]);
                           var0.fillRect(var4, var5, 23, 12);
                        } else if (var1 == 6) {
                           var0.setColor(p[var15][2].k[var6.a[7]][5]);
                           var0.fillRect(var4, var5, 23, 12);
                        } else if (var1 == 7) {
                           var0.setColor(p[var15][4].k[var6.a[9]][5]);
                           var0.fillRect(var4, var5, 23, 12);
                        }
                     } else if (var10 == 45) {
                        if (var1 == 3) {
                           var0.setColor(W[var6.a[12]]);
                           var0.fillRect(var4, var5, 23, 12);
                        } else if (var1 == 4) {
                           var12 = 1;
                           var8 = 192;
                        }
                     }
                  } else {
                     var12 = 1;
                     if (var1 == 2) {
                        var8 = 181;
                     } else if (var1 == 3) {
                        var9 = af;
                     }
                  }
               } else {
                  (var31 = n[0]).a = var4 << 8;
                  var31.b = var5 << 8;
                  var31.d.m = var6.b[15];
                  var31.a(var0);
                  var31.b();
                  if (var31.a()) {
                     ++c[0];
                     byte[] var10000 = c;
                     var10000[0] = (byte)(var10000[0] % (c.length - 1));
                     var31.a(c[c[0] + 1]);
                  }
               }
               break;
            case 18:
               var6 = s[0];
               var15 = ((var28 = 7 + var6.a[0] * 2) - 7) / 2;
               d var17 = null;
               int var16 = bF[1];
               int var19;
               label758:
               switch(y) {
               case 40:
               case 41:
               case 42:
               case 56:
               case 59:
               case 60:
               case 62:
               case 63:
               case 64:
                  boolean var32 = bF[2] == 0;
                  if (bF[0] == -1) {
                     int[] var23;
                     if (var10 == 2) {
                        switch(var1) {
                        case 10:
                           for(var19 = 0; var19 < 9; ++var19) {
                              var35 = 9;
                              if ((var36 = var19 + bF[3] * 9) == bF[1]) {
                                 var35 = 11;
                                 bF[6] = var4;
                                 bF[7] = var5 + var19 * 22;
                              }

                              if (var36 <= ac) {
                                 (var17 = v[14]).a(var35);
                                 var17.a = var4 << 8;
                                 var17.b = var5 + var19 * 22 << 8;
                                 if (var35 == 11) {
                                    b();
                                    b((var23 = var17.c())[0] >> 8, var23[1] >> 8, var23[2] - var23[0] >> 8, var23[3] - var23[1] >> 8, 3, 0);
                                 }

                                 bF[0] = var19;
                                 var17.a(var0);
                              }
                           }

                           bF[0] = -1;
                           if (bF[5] >= 2 && bF[5] == 2) {
                              if (var16 == 0) {
                                 h(0);
                              } else {
                                 h(ab[var16 - 1] + 1);
                              }
                           }
                        }
                     } else {
                        d var38;
                        if (var10 == 1) {
                           switch(var1) {
                           case 12:
                              for(var19 = 0; var19 < 9; ++var19) {
                                 if ((var36 = var19 + bF[3] * 9) == bF[1]) {
                                    var35 = 11;
                                    bF[6] = var4;
                                    bF[7] = var5 + var19 * 22;
                                 } else {
                                    var35 = 10;
                                 }

                                 if (var36 <= s[0].b[1] - 1) {
                                    (var17 = v[14]).a(var35);
                                    var17.a = var4 << 8;
                                    var17.b = var5 + var19 * 22 << 8;
                                    if (var35 == 11) {
                                       b();
                                       b((var23 = var17.c())[0] >> 8, var23[1] >> 8, var23[2] - var23[0] >> 8, var23[3] - var23[1] >> 8, 3, 0);
                                    }

                                    bF[0] = var19;
                                    var17.a(var0);
                                 }
                              }

                              bF[0] = -1;
                              break;
                           case 13:
                              if (bF[5] >= 2 && bF[5] == 2) {
                                 h(var16);
                              }
                              break;
                           case 14:
                              if (y == 63) {
                                 bF[0] = 1;
                                 (var38 = v[14]).a(14);
                                 var38.a = var4 << 8;
                                 var38.b = var5 << 8;
                                 var38.a(var0);
                                 bF[0] = -1;
                              }
                              break;
                           case 15:
                              if (y != 63) {
                                 var1 = var6.a[5];
                                 var0.setColor(p[var15][5].k[var1][2]);
                                 var0.fillRect(var4, var5, 23, 12);
                              }
                              break;
                           case 16:
                              var12 = 1;
                              var8 = 850;
                              break;
                           case 17:
                              var7 = 79;
                              break;
                           case 18:
                              var12 = 1;
                              var9 = f.e(10);
                           case 19:
                           default:
                              break;
                           case 20:
                              var12 = 1;
                              var8 = 170;
                           }
                        } else if (var10 == 0) {
                           switch(var1) {
                           case 12:
                              for(var19 = 0; var19 < 9; ++var19) {
                                 var35 = 9;
                                 if ((var36 = var19 + bF[3] * 9) == bF[1]) {
                                    var35 = 11;
                                    bF[6] = var4;
                                    bF[7] = var5 + var19 * 22;
                                 } else if (var36 == 0 || var32 && g.b(var36 - 1, -1) != -1 || !var32 && g.b(-1, var36 - 1) != -1) {
                                    var35 = 10;
                                 }

                                 if (var36 < bF[4]) {
                                    (var17 = v[14]).a(var35);
                                    var17.a = var4 << 8;
                                    var17.b = var5 + var19 * 22 << 8;
                                    if (var35 == 11) {
                                       b();
                                       b((var23 = var17.c())[0] >> 8, var23[1] >> 8, var23[2] - var23[0] >> 8, var23[3] - var23[1] >> 8, 3, 0);
                                    }

                                    bF[0] = var19;
                                    var17.a(var0);
                                 }
                              }

                              bF[0] = -1;
                              break;
                           case 13:
                              if (bF[5] >= 2 && bF[5] == 2) {
                                 h(var16);
                              }
                              break;
                           case 14:
                              if (y == 42) {
                                 bF[0] = 1;
                                 (var38 = v[14]).a(14);
                                 var38.a = var4 << 8;
                                 var38.b = var5 << 8;
                                 var38.a(var0);
                                 bF[0] = -1;
                              }
                              break;
                           case 15:
                              if (y != 42) {
                                 if (bF[2] == 0) {
                                    var1 = g.b(var16 - 1, -1);
                                 } else {
                                    var1 = g.b(-1, var16 - 1);
                                 }

                                 if (var1 == -1) {
                                    if (bF[2] == 0) {
                                       var1 = var6.a[7];
                                    } else {
                                       var1 = var6.a[9];
                                    }
                                 }

                                 var1 &= 255;
                                 var0.setColor(p[var15][2].k[var1][5]);
                                 var0.fillRect(var4, var5, 23, 12);
                              }
                              break;
                           case 16:
                              var12 = 1;
                              var8 = 850;
                              break;
                           case 17:
                              var7 = 79;
                              break;
                           case 18:
                              var12 = 1;
                              if (bF[1] != 0) {
                                 var9 = f.e(b[s[0].a[0]][bF[2]][bF[1]]);
                              } else {
                                 var9 = f.e(0);
                              }
                              break;
                           case 19:
                              if ((var1 = bF[1]) > 0) {
                                 if (bF[2] == 0) {
                                    var1 = g.b(var1 - 1, -1);
                                 } else {
                                    var1 = g.b(-1, var1 - 1);
                                 }

                                 if (var1 != -1) {
                                    var8 = 851;
                                 }
                              }
                           }
                        } else if (var10 == 3) {
                           if (var1 == 1) {
                              c(var0, var4, var5);
                           }
                        } else if (var10 == 4 || var10 == 6) {
                           var12 = 1;
                           var8 = 852 + (var1 - 1);
                        }
                     }
                  } else if (var10 == 14) {
                     if (var1 == 3) {
                        if (y == 42) {
                           var0.setColor(p[var15][2].k[bF[10]][5]);
                        } else if (y == 63) {
                           var0.setColor(p[var15][5].k[var6.a[5]][2]);
                        }

                        var0.fillRect(var4, var5, 23, 12);
                     }
                  } else {
                     var21 = bF[0];
                     var36 = bF[3] * 9 + var21;
                     boolean var41 = false;
                     boolean var24 = false;
                     var12 = 1;
                     if (y == 59 || y == 60) {
                        if (var36 == 0) {
                           var8 = 854;
                        } else {
                           var9 = GloftMMN.k(855) + " " + ab[var36 - 1];
                        }
                     }

                     if (y != 62 && y != 64 && y != 63) {
                        if (var36 == 0) {
                           var8 = 854;
                        } else {
                           var9 = GloftMMN.k(855) + " " + (var36 - 1);
                        }
                     } else if (var36 == 0) {
                        var8 = 854;
                     } else {
                        var9 = GloftMMN.k(184) + " " + (var36 - 1);
                     }
                  }
               case 43:
               case 44:
               case 45:
               case 46:
               case 47:
               case 50:
               case 51:
               case 52:
               case 55:
               case 57:
               case 58:
               case 61:
               default:
                  break;
               case 48:
               case 49:
                  if (var10 == 16) {
                     switch(var1) {
                     case 6:
                        v[17].d.a((Graphics)var0, 86, 0, var4, var5, 0, 0, 0);
                        (var31 = f.c[f.au]).a = var4 << 8;
                        var31.b = var5 << 8;
                        int[] var33 = var31.c();
                        var31.a -= var33[0] - (var4 << 8);
                        var31.b -= var33[1] - (var5 << 8);
                        var31.a -= (var33[2] - var33[0]) / 2;
                        var31.b -= var33[3] - var33[1];
                        var31.b += 3584;
                        var31.a(var0);
                     case 7:
                     default:
                        break label758;
                     case 8:
                        var12 = 1;
                        var8 = 850;
                        break label758;
                     case 9:
                        var7 = 79;
                        break label758;
                     case 10:
                        var12 = 1;
                        var9 = f.e(bF[19]);
                     }
                  } else if (var10 == 19) {
                     switch(var1) {
                     case 3:
                        var21 = (var31 = f.c[f.au]).d.m;
                        var0.setColor(var31.d.k[var21][1]);
                        var0.fillRect(var4, var5, 23, 12);
                     }
                  }
                  break;
               case 53:
               case 54:
                  a var18 = f.b[0];
                  if (var10 == 16) {
                     switch(var1) {
                     case 6:
                        int var20 = bM[f.bh * 4 + 2] & 255;
                        var21 = bM[f.bh * 4 + 3] & 255;
                        var18.m = var21;
                        int[] var22 = new int[]{-22, 0, -22, 22, -44, 11, 0, 11};

                        for(var19 = 0; var19 < 4; ++var19) {
                           var18.a((Graphics)var0, var20, var4 + var22[var19 * 2 + 0], var5 + var22[var19 * 2 + 1], 0, 0, 0);
                        }

                        var20 = bM[f.bh * 4 + 0] & 255;
                        var21 = bM[f.bh * 4 + 1] & 255;
                        var18.m = var21;
                        var18.a((Graphics)var0, var20, var4 - 22, var5 - 11, 0, 0, 0);
                        var18.a((Graphics)var0, var20, var4 - 44, var5 - 11 + 11, 0, 0, 0);
                        var18.a((Graphics)var0, var20 + 1, var4, var5 - 11, 0, 0, 0);
                        var18.a((Graphics)var0, var20 + 1, var4 + 22, var5 - 11 + 11, 0, 0, 0);
                     case 7:
                     default:
                        break;
                     case 8:
                        var12 = 1;
                        var8 = 850;
                        break;
                     case 9:
                        var7 = 79;
                        break;
                     case 10:
                        var12 = 1;
                        var9 = f.e(bF[16]);
                     }
                  } else if (var10 == 19) {
                     switch(var1) {
                     case 3:
                        byte var30 = 1;
                        if (y == 54) {
                           var30 = 3;
                        }

                        var0.setColor(var18.k[bM[f.bh * 4 + var30] & 255][2]);
                        var0.fillRect(var4, var5, 23, 12);
                     }
                  }
               }
            }
         }
      }

      if (var8 >= 0) {
         var9 = GloftMMN.k(var8);
      }

      if (var9 != null && var8 != 794 && var8 != 909 && var8 != 849 && var8 != 848 && var8 != 796 && var8 != 401 && var8 != 0 && var8 != 901 && var8 != 875) {
         M.a(var0, var9, var4, var5, var26, var12);
      } else if (var9 != null && (var8 == 794 || var8 == 909 || var8 == 901 || var8 == 875) && var5 > 200) {
         if (var4 < 120) {
            a(var0, var8, -1);
         } else {
            a(var0, -1, var8);
         }
      }

      if (var7 >= 0) {
         u[1].a((Graphics)var0, var7, 0, var4, var5, 0, 0, 0);
      }

   }

   private static void h(int var0) {
      g var1 = s[0];
      int var2;
      int var3 = ((var2 = 7 + var1.a[0] * 2) - 7) / 2;
      int var4 = var1.a[6];
      int var5 = var1.a[8];
      int var6 = var1.a[7] & 255;
      int var7 = var1.a[9] & 255;
      int var8;
      if (var0 > 0) {
         if (y >= 62 && y <= 64) {
            var1.a[4] = var0 - 1;
         } else if (bF[2] == 0) {
            if (y == 42) {
               var1.a[7] = bF[10];
            } else if ((var8 = g.b(var0 - 1, -1)) != -1) {
               var1.a[7] = var8;
            }

            var1.a[6] = var0 - 1;
         } else {
            if (y == 42) {
               var1.a[9] = bF[10];
            } else if ((var8 = g.b(-1, var0 - 1)) != -1) {
               var1.a[9] = var8;
            }

            var1.a[8] = var0 - 1;
         }
      }

      bF[11] = var1.a[8];
      bF[12] = var1.a[9] & 255;
      bF[13] = var1.a[6];
      bF[14] = var1.a[7] & 255;
      g.j[var3] = 0;
      f.b(0);

      for(var8 = 0; var8 < p.length; ++var8) {
         if (p[var8] != null) {
            for(int var9 = 0; var9 < p[var8].length; ++var9) {
               if (p[var8][var9] != null && p[var8][var9].w != null) {
                  p[var8][var9].w = (Image[][])null;
               }
            }
         }
      }

      System.gc();
      var1.a(var2, 0, true);
      var1.a[6] = var4;
      var1.a[8] = var5;
      var1.a[7] = var6;
      var1.a[9] = var7;
   }

   private static void a(g var0) {
      int[] var1 = new int[2];
      f.a(var0.c[0], var0.c[1], var1);
      short[] var3 = null;
      int var7;
      int var8;
      if (h != 18 && y == 1) {
         var7 = F - f.C;
         var8 = G - f.D;
         int var6;
         if ((var6 = GloftMMN.a((long)(var7 * var7 + var8 * var8))) < 22) {
            f.C = F;
            f.D = G;
            if (A == -1) {
               z = 0;
            } else {
               z = A;
            }

            A = -1;
         } else {
            var7 = (var7 << 8) / var6;
            var8 = (var8 << 8) / var6;
            var7 *= 22;
            var8 *= 22;
            f.C += var7 >> 8;
            f.D += var8 >> 8;
         }
      } else {
         if (y != 20 && y != 67) {
            boolean var4 = false;
            var7 = f.b(var1);
            boolean var5 = false;
            if (f.d || var7 != -1) {
               if ((var8 = f.a(var1)) != -1 && ((var3 = f.e[var8])[1] != F || var3[2] != G)) {
                  F = var3[1];
                  G = var3[2];
                  A = y;
                  z = 1;
               }

               if (f.d) {
                  f.E = -10000;
                  f.F = -10000;
                  if (var3 != null) {
                     F = var3[1];
                     G = var3[2];
                     f.C = F;
                     f.D = G;
                  } else {
                     F = -1;
                     G = -1;
                  }

                  if (y == 0) {
                     f.a(aB, true, true);
                  }
               }
            }
         }

      }
   }

   private static boolean d(int var0, int var1) {
      aB.setClip(0, 0, 240, 400);
      if (y == 73) {
         f.a(aB, false, true);
      } else {
         k = true;
      }

      byte var3 = 0;
      if (z == 73 || y == 73) {
         var3 = 1;
      }

      int var2;
      if (var1 == 1) {
         a(4 - var3, 0, true);
         U[2] = var0 + 0;
         U[3] = var0 + 1;

         for(var2 = 0; var2 < 4 - var3; ++var2) {
            R[var2] = var0 + var2 + 2;
         }

         if (var3 == 0) {
            R[3] = var0 + 11;
         }

         R[4] = var0 + 10;
         U[5] = -1;
      }

      var2 = U[0];
      if (var1 != 1 && aP) {
         if (h == 19) {
            aB.drawImage(Q, 0, 0, 20);
         }

         if (var2 == 0) {
            GloftMMN.f = !GloftMMN.f;
            if (GloftMMN.f) {
               if (var0 == 31) {
                  GloftMMN.c(5);
               } else {
                  GloftMMN.c(3);
               }
            } else {
               GloftMMN.e();
            }
         } else if (var2 == 1) {
            ax = (byte)((ax + 1) % 2);
            byte var4 = ag;
            if (ax == 1) {
               if (ag == 0) {
                  ag = 2;
               } else {
                  ag = 3;
               }
            } else if (ag != 0) {
               ag = 1;
            }

            a(true, false);
            ag = var4;
            if (ax == 1) {
               b(1000);
            }
         } else if (var2 == 2) {
            ai = (byte)((ai + 1) % 3);
            aj = false;
         } else {
            if (var2 == 3) {
               if (var3 != 0) {
                  return true;
               }

               a(20);
               e(var0 + 11, 1);
               return false;
            }

            if (var2 == 4) {
               return true;
            }
         }
      }

      byte var5 = 6;
      if (GloftMMN.f) {
         var5 = 5;
      }

      T[0] = " : " + GloftMMN.k(var0 + var5);
      var5 = 6;
      if (ax == 1) {
         var5 = 5;
      }

      T[1] = " : " + GloftMMN.k(var0 + var5);
      T[2] = " : " + GloftMMN.k(var0 + 7 + ai);
      if ((var1 == 1 || k) && Q != null) {
         aB.drawImage(Q, 120, 200, 3);
      }

      a(false);
      a(aB, false, false);
      return false;
   }

   private static boolean e(int var0, int var1) {
      aB.setClip(0, 0, 240, 400);
      k = true;
      int var2;
      if (var1 == 1) {
         if (ay < 0) {
            a(6, 0, false);
         } else {
            a(6, 0, true);
         }

         U[2] = var0;
         U[3] = 32;

         for(var2 = 0; var2 < 6; ++var2) {
            R[var2] = aA[var2];
         }

         if (ay >= 0) {
            R[6] = 41;
         }

         U[5] = -1;
      }

      var2 = U[0];
      if (var1 != 1 && aP) {
         aP = false;
         if (ay >= 0 && var2 == 6) {
            return true;
         }

         if (var2 != ay) {
            boolean var3 = ay < 0;
            ay = var2;
            GloftMMN.a("/3", 1);
            if (var3) {
               a(3);
            } else {
               e(var0, 1);
            }

            return false;
         }
      }

      if (ay < 0) {
         aB.setColor(0);
         aB.fillRect(0, 0, 240, 400);
      } else if ((var1 == 1 || k) && Q != null) {
         aB.drawImage(Q, 120, 200, 3);
      }

      a(false);
      a(aB, false, false);
      return false;
   }

   static {
      bW = bV;
      bX = 400 - bV;
      bY = true;
      bZ = true;
      aI = true;
      aJ = "";
      aS = new byte[1024];
      aT = new int[1024];
      aU = new int[1024];
      aV = 0;
      aW = false;
      aX = false;
      aY = false;
      aZ = new int[350];
      ba = 0;
      bb = new int[]{0, 0};
      bc = false;
      bd = new int[]{0, 0};
      be = false;
      bf = new int[]{0, 0, 0, 0};
      bg = false;
      int[] var10000 = new int[]{0, 0};
      bh = -1;
      bi = new int[]{0, 0};
      bj = new int[]{55, 57, 51, 49};
      bk = new int[]{52, 56, 54, 50};
      var10000 = new int[]{-1, -1};
      var10000 = new int[]{0, 0};
      var10000 = new int[]{0, 0};
      bu = new int[]{0, 0, 0, -107, 0, 107, -107, 0, 107, 0, -75, -75, -75, 75, 75, -75, 75, 75};
      bv = new int[]{0, -1, 0, 1, -1, 0, 1, 0, -1, -1, -1, 1, 1, -1, 1, 1};
      by = false;
      bz = -1;
      bA = -1;
      bB = -1;
      bC = -1;
      bD = -1;
      bE = -1;
      bF = new int[21];
      bG = false;
      bI = false;
      bJ = 3000L;
      bK = 0L;
      bL = 0L;
      bM = new byte[]{67, 10, 16, 66, 47, 70, 2, 0, 69, -56, 19, 66, 47, -56, 2, 70};
      bN = new int[]{41, 10, 71, 13, 64, 71, 80, 98, 130, 148, 160, 194, 201, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 43, 10, 128, 9, 13, 64, 80, 85, 97, 101, 128, 136, 137, 146, 160, 192, 194, 195, 200, 201, -1, -1, -1, 45, 10, 97, 9, 13, 64, 80, 97, 101, 136, 137, 146, 160, 192, 194, 195, 200, 201, -1, -1, -1, -1, -1, 47, 10, 1, 1, 10, 12, 13, 64, 70, 71, 80, 82, 85, 98, 101, 132, 200, -1, -1, -1, -1, -1, -1, 49, 10, 86, 10, 70, 81, 85, 86, 98, 101, 128, 136, 137, 147, 163, 194, 196, 197, 200, 201, -1, -1, -1, 51, 10, 13, 13, 64, 80, 97, 101, 137, 146, 192, 194, 196, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 53, 10, 13, 13, 64, 80, 97, 131, 136, 146, 160, 192, 194, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 55, 10, 9, 9, 13, 64, 81, 96, 101, 131, 136, 137, 146, 160, 192, 194, 196, 201, -1, -1, -1, -1, -1, 57, 10, 13, 13, 8, 64, 81, 98, 101, 136, 137, 147, 160, 192, 194, 196, 201, -1, -1, -1, -1, -1, -1, 61, 10, 13, 4, 13, 64, 71, 81, 97, 101, 135, 136, 137, 145, 146, 193, 194, 196, 197, 201, -1, -1, -1, 65, 10, 199, 12, 13, 64, 80, 97, 136, 137, 146, 160, 192, 194, 196, 199, 201, -1, -1, -1, -1, -1, -1, 67, 10, 10, 10, 4, 13, 64, 71, 81, 82, 97, 147, 160, 192, 194, 196, 200, -1, -1, -1, -1, -1, -1, 69, 10, 200, 1, 10, 64, 81, 82, 85, 98, 128, 136, 137, 146, 160, 194, 196, 197, 200, 201, -1, -1, -1};
      bO = new int[]{2, 0, 0, 13, 70, 80, 82, 86, 97, 100, 101, 136, 144, 145, 148, 162, 163, 192, 196, -1, -1, -1, 6, 6, 5, 6, 10, 64, 70, 71, 80, 82, 98, 101, 148, 194, 201, -1, -1, -1, -1, -1, -1, -1, 9, 81, 6, 9, 13, 81, 97, 100, 101, 148, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 14, 100, 13, 98, 100, 148, 194, 197, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 16, 66, 5, 9, 13, 66, 72, 80, 83, 97, 100, 101, 136, 144, 148, 160, 192, 194, 197, -1, -1, -1, 19, 66, 5, 9, 13, 66, 72, 80, 83, 97, 100, 101, 136, 144, 148, 160, 192, 194, 197, -1, -1, -1, 29, 132, 4, 13, 64, 81, 101, 132, 134, 136, 137, 148, 162, 194, 196, 197, -1, -1, -1, -1, -1, -1, 30, 81, 4, 13, 64, 81, 101, 132, 134, 136, 137, 148, 162, 194, 196, 197, -1, -1, -1, -1, -1, -1, 32, 136, 4, 9, 13, 64, 80, 97, 100, 136, 148, 160, 194, 198, 201, -1, -1, -1, -1, -1, -1, -1, 33, 101, 1, 13, 12, 12, 80, 97, 100, 101, 135, 136, 148, 162, 194, -1, -1, -1, -1, -1, -1, -1};
   }
}
